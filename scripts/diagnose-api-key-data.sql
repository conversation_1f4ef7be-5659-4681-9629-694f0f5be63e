-- API密钥数据诊断脚本
-- 用于检查和修复API密钥加密数据问题

-- ========================================
-- 1. 数据完整性检查
-- ========================================

-- 查看所有API密钥的基本信息
SELECT 
    '=== API密钥数据概览 ===' as info;

SELECT 
    COUNT(*) as total_keys,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_keys,
    COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_keys
FROM user_api_keys;

-- ========================================
-- 2. 加密数据格式检查
-- ========================================

SELECT 
    '=== 加密数据格式分析 ===' as info;

-- 检查所有记录的加密数据格式
SELECT 
    id,
    user_id,
    config_group_id,
    LENGTH(api_key_encrypted) as encrypted_length,
    LEFT(api_key_encrypted, 50) as encrypted_preview,
    CASE 
        WHEN api_key_encrypted IS NULL THEN 'NULL'
        WHEN api_key_encrypted = '' THEN 'EMPTY'
        WHEN api_key_encrypted REGEXP '^[0-9a-fA-F]+$' AND LENGTH(api_key_encrypted) % 2 = 0 THEN 'VALID_HEX'
        WHEN api_key_encrypted REGEXP '^[0-9a-fA-F]+$' AND LENGTH(api_key_encrypted) % 2 != 0 THEN 'HEX_ODD_LENGTH'
        ELSE 'INVALID_HEX'
    END as format_status,
    is_active,
    created_at
FROM user_api_keys 
ORDER BY id;

-- ========================================
-- 3. 问题数据统计
-- ========================================

SELECT 
    '=== 问题数据统计 ===' as info;

SELECT 
    format_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_api_keys), 2) as percentage
FROM (
    SELECT 
        CASE 
            WHEN api_key_encrypted IS NULL THEN 'NULL'
            WHEN api_key_encrypted = '' THEN 'EMPTY'
            WHEN api_key_encrypted REGEXP '^[0-9a-fA-F]+$' AND LENGTH(api_key_encrypted) % 2 = 0 THEN 'VALID_HEX'
            WHEN api_key_encrypted REGEXP '^[0-9a-fA-F]+$' AND LENGTH(api_key_encrypted) % 2 != 0 THEN 'HEX_ODD_LENGTH'
            ELSE 'INVALID_HEX'
        END as format_status
    FROM user_api_keys
) as status_summary
GROUP BY format_status
ORDER BY count DESC;

-- ========================================
-- 4. 识别具体的问题记录
-- ========================================

SELECT 
    '=== 问题记录详情 ===' as info;

-- 查找所有非有效十六进制格式的记录
SELECT 
    id,
    user_id,
    config_group_id,
    api_key_encrypted,
    LENGTH(api_key_encrypted) as length,
    is_active,
    created_at,
    CASE 
        WHEN api_key_encrypted IS NULL THEN 'NULL值'
        WHEN api_key_encrypted = '' THEN '空字符串'
        WHEN LENGTH(api_key_encrypted) % 2 != 0 THEN '长度为奇数'
        WHEN api_key_encrypted REGEXP '[^0-9a-fA-F]' THEN '包含非十六进制字符'
        ELSE '其他问题'
    END as problem_type
FROM user_api_keys 
WHERE NOT (
    api_key_encrypted IS NOT NULL 
    AND api_key_encrypted != ''
    AND api_key_encrypted REGEXP '^[0-9a-fA-F]+$' 
    AND LENGTH(api_key_encrypted) % 2 = 0
)
ORDER BY id;

-- ========================================
-- 5. 字符分析（查找非十六进制字符）
-- ========================================

SELECT 
    '=== 非十六进制字符分析 ===' as info;

-- 查找包含非十六进制字符的记录
SELECT 
    id,
    user_id,
    api_key_encrypted,
    LENGTH(api_key_encrypted) as length,
    -- 提取前100个字符进行分析
    SUBSTRING(api_key_encrypted, 1, 100) as first_100_chars,
    created_at
FROM user_api_keys 
WHERE api_key_encrypted REGEXP '[^0-9a-fA-F]'
ORDER BY id;

-- ========================================
-- 6. 长度分析
-- ========================================

SELECT 
    '=== 加密数据长度分析 ===' as info;

SELECT 
    LENGTH(api_key_encrypted) as encrypted_length,
    COUNT(*) as count,
    MIN(id) as min_id,
    MAX(id) as max_id,
    CASE 
        WHEN LENGTH(api_key_encrypted) % 2 = 0 THEN 'EVEN'
        ELSE 'ODD'
    END as length_type
FROM user_api_keys 
WHERE api_key_encrypted IS NOT NULL
GROUP BY LENGTH(api_key_encrypted)
ORDER BY encrypted_length;

-- ========================================
-- 7. 用户影响分析
-- ========================================

SELECT 
    '=== 用户影响分析 ===' as info;

-- 查看哪些用户受到影响
SELECT 
    u.user_id,
    COUNT(*) as total_keys,
    COUNT(CASE WHEN u.format_status = 'VALID_HEX' THEN 1 END) as valid_keys,
    COUNT(CASE WHEN u.format_status != 'VALID_HEX' THEN 1 END) as invalid_keys
FROM (
    SELECT 
        user_id,
        CASE 
            WHEN api_key_encrypted IS NULL THEN 'NULL'
            WHEN api_key_encrypted = '' THEN 'EMPTY'
            WHEN api_key_encrypted REGEXP '^[0-9a-fA-F]+$' AND LENGTH(api_key_encrypted) % 2 = 0 THEN 'VALID_HEX'
            ELSE 'INVALID_HEX'
        END as format_status
    FROM user_api_keys
) u
GROUP BY u.user_id
HAVING invalid_keys > 0
ORDER BY invalid_keys DESC, user_id;

-- ========================================
-- 8. 修复建议
-- ========================================

SELECT 
    '=== 修复建议 ===' as info;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM user_api_keys WHERE api_key_encrypted IS NULL OR api_key_encrypted = '') > 0 
        THEN 'CRITICAL: 发现空的加密数据，需要删除或重新加密'
        WHEN (SELECT COUNT(*) FROM user_api_keys WHERE api_key_encrypted REGEXP '[^0-9a-fA-F]') > 0
        THEN 'ERROR: 发现非十六进制字符，数据已损坏'
        WHEN (SELECT COUNT(*) FROM user_api_keys WHERE LENGTH(api_key_encrypted) % 2 != 0) > 0
        THEN 'WARNING: 发现奇数长度的十六进制数据'
        ELSE 'OK: 所有加密数据格式正常'
    END as status,
    (SELECT COUNT(*) FROM user_api_keys WHERE NOT (
        api_key_encrypted IS NOT NULL 
        AND api_key_encrypted != ''
        AND api_key_encrypted REGEXP '^[0-9a-fA-F]+$' 
        AND LENGTH(api_key_encrypted) % 2 = 0
    )) as problem_count,
    (SELECT COUNT(*) FROM user_api_keys) as total_count;

-- ========================================
-- 9. 清理脚本（谨慎使用）
-- ========================================

-- 注意：以下脚本仅供参考，执行前请备份数据！

/*
-- 选项A：删除损坏的记录
DELETE FROM user_api_keys 
WHERE NOT (
    api_key_encrypted IS NOT NULL 
    AND api_key_encrypted != ''
    AND api_key_encrypted REGEXP '^[0-9a-fA-F]+$' 
    AND LENGTH(api_key_encrypted) % 2 = 0
);

-- 选项B：将损坏的记录标记为无效
UPDATE user_api_keys 
SET is_active = FALSE,
    api_key_encrypted = 'CORRUPTED_DATA'
WHERE NOT (
    api_key_encrypted IS NOT NULL 
    AND api_key_encrypted != ''
    AND api_key_encrypted REGEXP '^[0-9a-fA-F]+$' 
    AND LENGTH(api_key_encrypted) % 2 = 0
);

-- 选项C：清理相关的负载均衡数据
DELETE FROM api_key_load_balance 
WHERE api_key_id IN (
    SELECT id FROM user_api_keys 
    WHERE NOT (
        api_key_encrypted IS NOT NULL 
        AND api_key_encrypted != ''
        AND api_key_encrypted REGEXP '^[0-9a-fA-F]+$' 
        AND LENGTH(api_key_encrypted) % 2 = 0
    )
);
*/
