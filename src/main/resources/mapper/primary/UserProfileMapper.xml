<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.UserProfileMapper">

    <!-- UserProfile 结果映射 -->
    <resultMap id="UserProfileResultMap" type="com.example.pure.model.entity.UserProfile">
        <!-- Assuming UserProfile DTO has these fields. Add id if present/needed -->
        <id property="id" column="id"/> <!-- Assuming primary/foreign key is id -->
        <result property="username" column="username"/> <!-- Still selecting username for display/convenience? -->
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="description" column="description"/>
    </resultMap>

    <!-- 插入新的用户信息，关联到用户ID -->
    <insert id="createNewUserProfile" parameterType="com.example.pure.model.entity.UserProfile">
        <!-- Assuming user_profile table has id as PK/FK and potentially username, phone, etc. -->
        <!-- Adjust columns based on your actual user_profile table structure -->
        INSERT INTO user_profile(id, username, phone, email, nickname, avatar, description)
        VALUES(#{id}, #{username}, #{phone}, #{email}, #{nickname}, #{avatar}, #{description})
        <!-- Consider ON DUPLICATE KEY UPDATE if you want to handle existing id -->
    </insert>

    <!-- 根据用户ID查找用户详细信息 -->
    <select id="findUserProfileByUserId" resultMap="UserProfileResultMap">
        SELECT id, username, phone, email, nickname, avatar, description
        FROM user_profile
        WHERE id = #{id}
    </select>

    <!-- 根据用户名查找用户详细信息 - Requires JOIN -->
    <select id="findUserProfileByUsername" resultMap="UserProfileResultMap">
        SELECT id, username, phone, email, nickname, avatar, description
        FROM user_profile
        WHERE username = #{username}
    </select>

    <!-- 根据用户ID更新用户详细信息 -->
    <update id="updateUserProfileByUserId" parameterType="com.example.pure.model.entity.UserProfile">
        UPDATE user_profile
        SET
            phone = #{phone},
            email = #{email},
            nickname = #{nickname},
            avatar = #{avatar},
            description = #{description},
            username = #{username} /* Update username here too? Or should it be read-only? */
        WHERE id = #{id}
    </update>

    <!-- 根据用户名更新用户详细信息 -->
    <update id="updateUserProfileByUserUsername" parameterType="com.example.pure.model.entity.UserProfile">
        UPDATE user_profile
        SET
            phone = #{phone},
            email = #{email},
            nickname = #{nickname},
            avatar = #{avatar},
            description = #{description},
            username = #{username} /* Update username here too? Or should it be read-only? */
        WHERE username = #{username}
    </update>

    <!-- 根据邮箱查找用户ID (从 user_profile 查找) -->
    <select id="findUserIdByEmail" resultType="java.lang.Long">
        SELECT id FROM user_profile WHERE email = #{email} LIMIT 1
    </select>
    <!-- 删除用户详细信息 (如果需要) -->
    <delete id="deleteUserProfileByUserId">
        DELETE FROM user_profile WHERE id = #{id}
    </delete>

</mapper>
