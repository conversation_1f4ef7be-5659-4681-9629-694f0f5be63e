<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.pure.mapper.primary.UserRoleMapper">
    <!-- 角色结果映射 -->
    <resultMap id="RoleResultMap" type="com.example.pure.model.entity.Role">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
    </resultMap>
    <!-- 为用户分配角色 -->
    <insert id="assignRoleToUser">
        INSERT INTO user_roles (user_id, role_id)
        VALUES (#{userId}, #{roleId})
    </insert>

    <!-- 获取用户的所有角色 -->
    <select id="findRolesByUserId" resultMap="RoleResultMap">
        SELECT r.id, r.name, r.created_time, r.updated_time
        FROM roles r
                 INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>

    <!-- 移除用户的角色 -->
    <delete id="removeRoleFromUser">
        DELETE
        FROM user_roles
        WHERE user_id = #{userId}
          AND role_id = #{roleId}
    </delete>

    <!-- 获取具有指定角色的所有用户ID -->
    <select id="findUserIdsByRoleId" resultType="long">
        SELECT user_id
        FROM user_roles
        WHERE role_id = #{roleId}
    </select>
</mapper>
