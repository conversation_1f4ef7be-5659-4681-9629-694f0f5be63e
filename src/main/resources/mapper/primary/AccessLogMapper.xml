<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.AccessLogMapper">

    <resultMap id="AccessLogResultMap" type="com.example.pure.model.entity.AccessLog">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="accessType" column="access_type"/>
        <result property="accessCount" column="access_count"/>
        <result property="accessDate" column="access_date"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="ipAddress" column="ip_address"/>
    </resultMap>
<!--防止SQL注入#{}为参数传递，不会把用户输入的数据当做SQL代码，数据库预编译 SQL 模板，用户输入会被视为纯数据-->
    <select id="findByUserIdAndTypeAndDate" resultMap="AccessLogResultMap">
        SELECT * FROM access_log
        WHERE user_id = #{userId}
        AND access_type = #{accessType}
        AND DATE(access_date) = #{accessDate}
    </select>

    <insert id="insert" parameterType="com.example.pure.model.entity.AccessLog" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO access_log (
            user_id,
            access_type,
            access_count,
            access_date,
            created_time,
            updated_time,
            ip_address
        ) VALUES (
            #{userId},
            #{accessType},
            #{accessCount},
            #{accessDate},
            #{createdTime},
            #{updatedTime},
            #{ipAddress}
        )
    </insert>

    <update id="updateAccessCount" parameterType="com.example.pure.model.entity.AccessLog">
        UPDATE access_log
        SET access_count = access_count + 1,
            updated_time = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
        AND access_type = #{accessType}
        AND access_date = #{accessDate}
    </update>
</mapper>
