<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper">

    <!--设置查询类型为List-->
    <insert id="insertVideoInfoTypeLink" parameterType="java.util.List"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO video_info_type_link(
            video_info_id, type_id, created_time, updated_time
        )
        VALUES
        <foreach collection="linkList" item="linkItem" separator=",">
            (
                #{linkItem.videoInfoId}, #{linkItem.typeId}, NOW(), NOW()
            )
        </foreach>
    </insert>

</mapper>
