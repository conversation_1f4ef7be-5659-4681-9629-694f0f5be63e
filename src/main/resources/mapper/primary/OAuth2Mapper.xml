<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.OAuth2Mapper">


    <insert id="insertOAuth2Info" parameterType="com.example.pure.model.entity.OAuth2" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO o_auth2 ( user_id,provider,access_token, access_token_expire_In, refresh_token, refresh_token_expire_in, created_time, updated_time )
        VALUES (#{userId}, #{provider},#{accessToken}, #{accessTokenExpireIn},  #{refreshToken}, #{refreshTokenExpireIn}, NOW(), NOW())
    </insert>


    <select id="findOAuth2InfoByUserIdAndProvider" resultType="com.example.pure.model.entity.OAuth2">
        SELECT * FROM o_auth2 WHERE user_id = #{userId} AND provider = #{provider}
    </select>

    <update id="updateOAuth2Info" parameterType="com.example.pure.model.entity.OAuth2">
        UPDATE o_auth2
        SET access_token = #{accessToken},
            access_token_expire_in = #{accessTokenExpireIn},
            refresh_token = #{refreshToken},
            refresh_token_expire_in = #{refreshTokenExpireIn},
            updated_time = NOW()
        WHERE user_id = #{userId} AND provider = #{provider}
    </update>
</mapper>
