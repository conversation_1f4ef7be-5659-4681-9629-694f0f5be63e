<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.pure.mapper.primary.PureVideoTypeMapper">

    <insert id="insertVideoType" parameterType="com.example.pure.model.entity.VideoType"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO video_type(
            name
        )
        VALUES (
            #{name}
        )
    </insert>

    <select id="findVideoTypeByName" resultType="com.example.pure.model.entity.VideoType">
        SELECT * FROM video_type WHERE name = #{name}
    </select>

    <!--如果返回类型为List的话会自动把多行数据反序列话为List-->
    <select id="findVideoTypeByLinkWithVideoId" resultType="com.example.pure.model.entity.VideoType">
        SELECT  vt.name,vt.id FROM video_type vt
        JOIN  video_info_type_link vitl ON vt.id =vitl.type_id
        WHERE vitl.video_info_id =#{videoInfoId}

    </select>

    <select id="findAllVideoType" resultType="com.example.pure.model.entity.VideoType">
        SELECT * FROM video_type
    </select>
</mapper>
