<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- API密钥负载均衡状态Mapper配置 -->
<mapper namespace="com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper">

    <!-- API密钥负载均衡状态结果映射 -->
    <resultMap id="ApiKeyLoadBalanceResultMap" type="com.example.pure.model.entity.ApiKeyLoadBalance">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="configGroupId" column="config_group_id"/>
        <result property="apiKeyId" column="api_key_id"/>
        <result property="currentRequests" column="current_requests"/>
        <result property="totalRequests" column="total_requests"/>
        <result property="errorCount" column="error_count"/>
        <result property="lastErrorAt" column="last_error_at"/>
        <result property="isHealthy" column="is_healthy"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, user_id, config_group_id, api_key_id, current_requests,
        total_requests, error_count, last_error_at, is_healthy, updated_at
    </sql>

    <!-- 根据用户ID和提供商查询负载均衡状态 -->
    <select id="selectByUserIdAndProvider" resultMap="ApiKeyLoadBalanceResultMap">
        SELECT lb.id, lb.user_id, lb.config_group_id, lb.api_key_id, lb.current_requests,
               lb.total_requests, lb.error_count, lb.last_error_at, lb.is_healthy, lb.updated_at
        FROM api_key_load_balance lb
        INNER JOIN user_ai_config_groups g ON lb.config_group_id = g.id
        WHERE lb.user_id = #{userId} AND g.provider = #{provider}
    </select>

    <!-- 根据API密钥ID查询负载均衡状态 -->
    <select id="selectByApiKeyId" resultMap="ApiKeyLoadBalanceResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM api_key_load_balance
        WHERE api_key_id = #{apiKeyId}
    </select>

    <!-- 查询健康的API密钥负载状态 -->
    <select id="selectHealthyByUserIdAndProvider" resultMap="ApiKeyLoadBalanceResultMap">
        SELECT lb.id, lb.user_id, lb.config_group_id, lb.api_key_id, lb.current_requests,
               lb.total_requests, lb.error_count, lb.last_error_at, lb.is_healthy, lb.updated_at
        FROM api_key_load_balance lb
        INNER JOIN user_ai_config_groups g ON lb.config_group_id = g.id
        WHERE lb.user_id = #{userId} AND g.provider = #{provider}
        AND lb.is_healthy = TRUE
        ORDER BY lb.current_requests ASC, lb.total_requests ASC
    </select>

    <!-- 插入负载均衡状态 -->
    <insert id="insert" parameterType="com.example.pure.model.entity.ApiKeyLoadBalance"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO api_key_load_balance (
            user_id, config_group_id, api_key_id, current_requests,
            total_requests, error_count, is_healthy, updated_at
        ) VALUES (
            #{userId}, #{configGroupId}, #{apiKeyId}, #{currentRequests},
            #{totalRequests}, #{errorCount}, #{isHealthy}, #{updatedAt}
        )
    </insert>

    <!-- 更新负载均衡状态 -->
    <update id="updateById" parameterType="com.example.pure.model.entity.ApiKeyLoadBalance">
        UPDATE api_key_load_balance
        SET current_requests = #{currentRequests},
            total_requests = #{totalRequests},
            error_count = #{errorCount},
            is_healthy = #{isHealthy},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 增加当前请求数 -->
    <update id="incrementRequests">
        UPDATE api_key_load_balance
        SET current_requests = current_requests + 1,
            total_requests = total_requests + 1,
            updated_at = NOW()
        WHERE api_key_id = #{apiKeyId}
    </update>

    <!-- 减少当前请求数 -->
    <update id="decrementRequests">
        UPDATE api_key_load_balance
        SET current_requests = GREATEST(current_requests - 1, 0),
            updated_at = NOW()
        WHERE api_key_id = #{apiKeyId}
    </update>

    <!-- 增加错误计数 -->
    <update id="incrementErrorCount">
        UPDATE api_key_load_balance
        SET error_count = error_count + 1,
            last_error_at = NOW(),
            updated_at = NOW()
        WHERE api_key_id = #{apiKeyId}
    </update>

    <!-- 更新健康状态 -->
    <update id="updateHealthStatus">
        UPDATE api_key_load_balance
        SET is_healthy = #{isHealthy},
            updated_at = NOW()
        WHERE api_key_id = #{apiKeyId}
    </update>

    <!-- 删除负载均衡状态 -->
    <delete id="deleteByApiKeyId">
        DELETE FROM api_key_load_balance
        WHERE api_key_id = #{apiKeyId}
    </delete>
</mapper>
