package com.example.pure.serializer;

import com.example.pure.annotation.DataMasking;
import com.example.pure.annotation.MaskingStrategy;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 通用数据脱敏序列化器
 * <p>
 * 该序列化器用于在JSON序列化过程中对敏感数据进行脱敏处理。
 * 配合 {@link DataMasking} 注解和 {@link MaskingStrategy} 枚举使用。
 * </p>
 * <p>
 * 使用示例:
 * <pre>
 * {@code
 * @JsonSerialize(using = GenericDataMaskingSerializer.class)
 * @DataMasking(strategy = MaskingStrategy.USERNAME)
 * private String username;
 * }
 * </pre>
 * </p>
 */
public class GenericDataMaskingSerializer extends JsonSerializer<String> implements ContextualSerializer {

    // 生成logger日志
    private static final Logger log = LoggerFactory.getLogger(GenericDataMaskingSerializer.class);
    // 将正则表达式编译成 Pattern 对象可以提高匹配效率，因为编译只需要进行一次，之后可以重复使用该 Pattern 对象进行多次匹配
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^(.+)@(.+)$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^(\\d{3})(\\d{4})(\\d{4})$");

    private MaskingStrategy strategy;

    /**
     * 默认构造器
     */
    public GenericDataMaskingSerializer() {
        this.strategy = MaskingStrategy.NONE;
    }

    /**
     * 带策略的构造器，用于 {@link ContextualSerializer} 实现
     *
     * @param strategy 指定的脱敏策略
     */
    public GenericDataMaskingSerializer(MaskingStrategy strategy) {
        this.strategy = strategy;
    }

    /**
     * 序列化方法，根据指定的脱敏策略对数据进行处理
     *
     * @param value 需要被序列化的原始字符串值
     * @param gen JSON生成器，生成 JSON 输出的核心组件
     * @param serializers 序列化提供者，提供对其他 Serializer 访问的 "提供者" 或 "上下文"
     * @throws IOException 如果序列化过程中发生I/O错误
     */
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        try {
            String maskedValue = applyMaskingStrategy(value, strategy);
            gen.writeString(maskedValue);
        } catch (Exception e) {
            log.warn("数据脱敏处理异常，字段将以原值输出: {}", e.getMessage());
            gen.writeString(value);
        }
    }

    /**
     * 应用脱敏策略
     *
     * @param value 原始值
     * @param strategy 脱敏策略
     * @return 脱敏后的值
     */
    private String applyMaskingStrategy(String value, MaskingStrategy strategy) {
        if (value == null || value.isEmpty()) {
            return value;
        }

        switch (strategy) {
            case PASSWORD:
                return "******";
            case EMAIL:
                return maskEmail(value);
            case USERNAME:
                return maskUsername(value);
            case PHONE:
                return maskPhone(value);
            case ID_CARD:
                return maskIdCard(value);
            case OPENAI_API_KEY:
                return maskOpenaiApiKey(value);
            case ADDRESS:
                return maskAddress(value);
            case BANK_CARD:
                return maskBankCard(value);
            case DEFAULT:
                return value.length() > 2 ? value.charAt(0) + "****" + value.charAt(value.length() - 1) : "****";
            case NONE:
            default:
                return value;
        }
    }

    /**
     * 邮箱脱敏：显示首尾字符，中间用星号代替
     * 例如：a****<EMAIL>
     *
     * @param email 邮箱地址
     * @return 脱敏后的邮箱
     */
    private String maskEmail(String email) {
        if (email == null || email.isEmpty()) {
            return "****";
        }

        // 创建匹配器
        Matcher matcher = EMAIL_PATTERN.matcher(email);
        // 开始匹配符合正则表达式要求返回布尔值
        if (matcher.matches()) {
            String username = email.substring(0, email.indexOf('@'));
            String domain = email.substring(email.indexOf('@'));

            if (username.length() <= 2) {
                return "**" + domain;
            }

            // 返回字符串索引字符拼接+username字符串最大长度-2+username最后一个字符串+邮箱域名
            return username.charAt(0) +
                   repeatChar('*', Math.max(0, username.length() - 2)) +
                   username.charAt(username.length() - 1) +
                   domain;
        }

        // 邮箱格式不符合规范，返回默认脱敏
        return email.length() > 2 ?
               email.substring(0, 1) + "****" + email.substring(email.length() - 1) :
               "****";
    }

    /**
     * 用户名脱敏：保留首尾字符，中间用星号代替
     *
     *
     * @param username 用户名
     * @return 脱敏后的用户名
     */
    private String maskUsername(String username) {
        if (username == null || username.isEmpty()) {
            return "****";
        }

        int len = username.length();
        if (len <= 1) {
            return "*";
        } else if (len == 2) {
            return username.substring(0, 1) + "*";
        } else if (len == 3) {
            return username.substring(0, 1) + "*" + username.substring(2);
        } else {
            return username.substring(0, 1) +
                   repeatChar('*', len - 2) +
                   username.substring(len - 1);
        }
    }

    /**
     * 手机号脱敏：保留前3位和后4位，中间用星号代替
     * 例如：138****1234
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return "****";
        }

        Matcher matcher = PHONE_PATTERN.matcher(phone);
        if (matcher.matches()) {
            return matcher.group(1) + "****" + matcher.group(3);
        }

        // 如果不是标准11位手机号，仅保留前3位和后4位
        int len = phone.length();
        if (len <= 7) {
            return "****" + (len > 4 ? phone.substring(len - 4) : phone);
        } else {
            return phone.substring(0, 3) + "****" + phone.substring(len - 4);
        }
    }

    /**
     * 身份证号脱敏：保留前6位和后4位，中间用星号代替
     * 例如：110101********1234
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.isEmpty()) {
            return "****";
        }

        int len = idCard.length();
        if (len <= 10) {
            return idCard.substring(0, Math.min(len, 6)) + "****";
        } else {
            return idCard.substring(0, 6) + "********" + idCard.substring(len - 4);
        }
    }

    private String maskOpenaiApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * 地址脱敏：保留省市区信息，详细地址用星号代替
     * 例如：北京市朝阳区****
     *
     * @param address 地址
     * @return 脱敏后的地址
     */
    private String maskAddress(String address) {
        if (address == null || address.isEmpty()) {
            return "****";
        }

        // 简单处理：假设前10个字符为省市区信息
        int cutPoint = Math.min(address.length(), 10);
        return address.substring(0, cutPoint) + "****";
    }

    /**
     * 银行卡号脱敏：保留前6位和后4位，中间用星号代替
     * 例如：622848********1234
     *
     * @param bankCard 银行卡号
     * @return 脱敏后的银行卡号
     */
    private String maskBankCard(String bankCard) {
        if (bankCard == null || bankCard.isEmpty()) {
            return "****";
        }

        int len = bankCard.length();
        if (len <= 10) {
            return bankCard.substring(0, Math.min(len, 6)) + "****";
        } else {
            return bankCard.substring(0, 6) + "********" + bankCard.substring(len - 4);
        }
    }

    /**
     * 实现 {@link ContextualSerializer} 接口的方法
     * 用于根据字段上的注解信息创建对应的序列化器实例
     *
     * @param prov 序列化提供者
     * @param property Bean属性
     * @return 上下文序列化器
     * @throws JsonMappingException 如果创建序列化器失败
     */
    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        if (property != null) {
            DataMasking annotation = property.getAnnotation(DataMasking.class);
            if (annotation != null) {
                return new GenericDataMaskingSerializer(annotation.strategy());
            }
        }
        return this;
    }

    /**
     * 重复字符以兼容Java 8 (替代 String.repeat())
     *
     * @param ch 要重复的字符
     * @param count 重复次数
     * @return 重复指定次数的字符组成的字符串
     */
    private static String repeatChar(char ch, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder(count);
        for (int i = 0; i < count; i++) {
            sb.append(ch);
        }
        return sb.toString();
    }
}
