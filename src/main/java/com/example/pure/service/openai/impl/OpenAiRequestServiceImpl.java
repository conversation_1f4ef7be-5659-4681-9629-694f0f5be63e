package com.example.pure.service.openai.impl;

import com.example.pure.model.dto.response.openai.OpenAiRequestLogInfo;
import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.dto.request.openai.OpenAiImageRequest;
import com.example.pure.model.dto.response.openai.OpenAiImageResponse;
import com.example.pure.model.dto.response.openai.OpenAiModelResponse;
import com.example.pure.service.openai.OpenAiCompatibleService;
import com.example.pure.service.openai.OpenAiRequestService;
import com.example.pure.util.MultimodalContentUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * OpenAI请求处理服务实现
 * <p>
 * 负责处理OpenAI兼容API的业务逻辑，包括参数验证、日志记录和业务处理
 * </p>
 */
@Slf4j
@Service
public class OpenAiRequestServiceImpl implements OpenAiRequestService {
    private final OpenAiCompatibleService openAiCompatibleService;

    /**
     * 构造函数注入依赖
     *
     * @param openAiCompatibleService OpenAI兼容服务
     */
    @Autowired
    public OpenAiRequestServiceImpl(OpenAiCompatibleService openAiCompatibleService) {
        this.openAiCompatibleService = openAiCompatibleService;
    }



    @Override
    public Object processChatCompletions(String apiKey, OpenAiChatRequest request, OpenAiRequestLogInfo logInfo) {
        // 1. 参数验证
        validateRequest(request);

        // 2. 多模态内容验证
        validateMultimodalContent(request);

        // 3. 记录请求信息
        logChatRequestInfo(request);

        if (Boolean.TRUE.equals(request.getStream())) {
            log.debug("处理流式聊天请求 - 模型: {}", request.getModel());
            return openAiCompatibleService.streamChatCompletions(apiKey, request, logInfo);
        } else {
            log.debug("处理非流式聊天请求 - 模型: {}", request.getModel());
            return openAiCompatibleService.chatCompletions(apiKey, request, logInfo);
        }
    }

    @Override
    public OpenAiImageResponse processImageGeneration(String apiKey, OpenAiImageRequest request, OpenAiRequestLogInfo logInfo) {
        // 1. 参数验证
        validateRequest(request);

        // 2. 记录请求信息
        log.info("收到OpenAI兼容图片生成请求 - 模型: {}, 提示词长度: {}",
                request.getModel(), request.getPrompt().length());

        // 3. 调用服务处理
        return openAiCompatibleService.generateImages(apiKey, request, logInfo);
    }

    @Override
    public OpenAiModelResponse processModelList(String apiKey) {
        log.debug("收到OpenAI兼容模型列表请求");
        return openAiCompatibleService.listModels(apiKey);
    }

    @Override
    public void validateRequest(Object request) throws IllegalArgumentException {
        if (request == null) {
            throw new IllegalArgumentException("请求对象不能为空");
        }

        // 根据请求类型进行具体验证
        if (request instanceof OpenAiChatRequest) {
            validateChatRequest((OpenAiChatRequest) request);
        } else if (request instanceof OpenAiImageRequest) {
            validateImageRequest((OpenAiImageRequest) request);
        }
    }

    /**
     * 验证聊天请求参数
     */
    private void validateChatRequest(OpenAiChatRequest request) {
        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }

        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            throw new IllegalArgumentException("模型名称不能为空");
        }
    }

    /**
     * 验证图片生成请求参数
     */
    private void validateImageRequest(OpenAiImageRequest request) {
        if (request.getPrompt() == null || request.getPrompt().trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }

        if (request.getModel() == null || request.getModel().trim().isEmpty()) {
            throw new IllegalArgumentException("模型名称不能为空");
        }
    }

    /**
     * 验证多模态内容
     */
    private void validateMultimodalContent(OpenAiChatRequest request) {
        for (OpenAiChatRequest.OpenAiMessage message : request.getMessages()) {
            if (message.getContent() == null || message.getContent().isEmpty()) {
                throw new IllegalArgumentException("消息内容不能为空");
            }

            MultimodalContentUtils.ValidationResult result =
                MultimodalContentUtils.validateMultimodalContent(message.getContent());

            if (!result.isValid()) {
                throw new IllegalArgumentException(result.getErrorMessage());
            }
        }
    }

    /**
     * 从console记录聊天请求信息（包含多模态统计）
     */
    private void logChatRequestInfo(OpenAiChatRequest request) {
        int totalImages = 0;
        boolean hasMultimodal = false;

        for (OpenAiChatRequest.OpenAiMessage message : request.getMessages()) {
            if (message.getContent() != null && MultimodalContentUtils.containsImages(message.getContent())) {
                hasMultimodal = true;
                totalImages += MultimodalContentUtils.getImageCount(message.getContent());
            }
        }

        if (hasMultimodal) {
            log.info("收到OpenAI兼容多模态聊天请求 - 模型: {}, 流式: {}, 消息数: {}, 图片总数: {}",
                    request.getModel(), request.getStream(), request.getMessages().size(), totalImages);
        } else {
            log.info("收到OpenAI兼容聊天请求 - 模型: {}, 流式: {}, 消息数: {}",
                    request.getModel(), request.getStream(), request.getMessages().size());
        }
    }
}
