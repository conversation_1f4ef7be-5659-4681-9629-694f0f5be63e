package com.example.pure.service.openai;

import com.example.pure.model.dto.response.openai.OpenAiRequestLogInfo;
import com.example.pure.model.dto.request.openai.OpenAiChatRequest;
import com.example.pure.model.dto.request.openai.OpenAiImageRequest;
import com.example.pure.model.dto.response.openai.OpenAiChatResponse;
import com.example.pure.model.dto.response.openai.OpenAiImageResponse;
import com.example.pure.model.dto.response.openai.OpenAiModelResponse;
import com.example.pure.model.dto.response.openai.OpenAiApiKeyValidationResult;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * OpenAI兼容服务接口
 * <p>
 * 提供OpenAI API格式的服务接口，处理认证、负载均衡和格式转换
 * </p>
 */
public interface OpenAiCompatibleService {

    /**
     * 处理流式聊天完成请求
     * <p>
     * 解析兼容格式的API密钥，选择合适的后端API，返回SSE流式响应
     * </p>
     *
     * @param compatibleApiKey 兼容格式的API密钥
     * @param request          OpenAI格式的聊天请求
     * @param logInfo          请求日志信息对象
     * @return SSE发射器
     */
    SseEmitter streamChatCompletions(String compatibleApiKey, OpenAiChatRequest request, OpenAiRequestLogInfo logInfo);

    /**
     * 处理非流式聊天完成请求
     * <p>
     * 解析兼容格式的API密钥，选择合适的后端API，返回完整响应
     * </p>
     *
     * @param compatibleApiKey 兼容格式的API密钥
     * @param request          OpenAI格式的聊天请求
     * @param logInfo          请求日志信息对象
     * @return OpenAI格式的聊天响应
     */
    OpenAiChatResponse chatCompletions(String compatibleApiKey, OpenAiChatRequest request, OpenAiRequestLogInfo logInfo);

    /**
     * 获取可用模型列表
     * <p>
     * 根据用户的API密钥配置，返回所有可用的模型列表
     * </p>
     *
     * @param compatibleApiKey 兼容格式的API密钥
     * @return OpenAI格式的模型列表响应
     */
    OpenAiModelResponse listModels(String compatibleApiKey);

    /**
     * 处理图片生成请求
     * <p>
     * 完全兼容OpenAI的/v1/images/generations端点，支持多个AI提供商的图片生成服务。
     * 该方法会自动解析兼容格式的API密钥，根据模型名称选择合适的后端API提供商，
     * 并处理不同提供商之间的参数差异和响应格式统一。
     * </p>
     *
     * <h3>支持的提供商：</h3>
     * <ul>
     *   <li><strong>OpenAI:</strong> DALL-E 2/3系列模型，支持高质量图片生成</li>
     *   <li><strong>Google:</strong> Imagen 3.0系列模型，支持快速和标准两种模式</li>
     *   <li><strong>不支持:</strong> Claude系列（Anthropic不提供图片生成功能）</li>
     * </ul>
     *
     * <h3>核心功能：</h3>
     * - 智能提供商选择和负载均衡
     * - 参数兼容性验证和转换
     * - 统一的错误处理和重试机制
     * - OpenAI格式的响应标准化
     *
     * @param compatibleApiKey 兼容格式的API密钥，包含用户身份和权限信息
     * @param request OpenAI格式的图片生成请求，包含prompt、模型、尺寸、数量等参数
     * @param logInfo 请求日志信息对象
     * @return OpenAI格式的图片生成响应，包含生成的图片URL列表和相关元数据
     * @throws RuntimeException 当API密钥无效、模型不支持图片生成或生成过程失败时
     */
    OpenAiImageResponse generateImages(String compatibleApiKey, OpenAiImageRequest request, OpenAiRequestLogInfo logInfo);

    /**
     * 验证兼容格式的API密钥
     * <p>
     * 解析并验证兼容格式的API密钥是否有效
     * </p>
     *
     * @param compatibleApiKey 兼容格式的API密钥
     * @return 验证结果
     */
    OpenAiApiKeyValidationResult validateCompatibleApiKey(String compatibleApiKey);

    // ========================
    // 注意：相关的数据传输对象已移动到独立的包中
    // 请参考：com.example.pure.model.openai 包
    // ========================
}
