package com.example.pure.service.openai;

import com.example.pure.common.PageFinalResult;
import com.example.pure.mapper.primary.OpenAiRequestLogMapper;
import com.example.pure.model.dto.response.openai.OpenAiRequestLogInfo;
import com.example.pure.model.dto.request.log.RequestLogQueryRequest;
import com.example.pure.model.dto.response.openai.OpenAiRequestLogResponse;
import com.example.pure.model.entity.OpenAiRequestLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * OpenAI请求日志服务
 * <p>
 * 负责记录OpenAI请求的详细日志信息，并提供查询功能
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpenAiRequestLogService {

    private final OpenAiRequestLogMapper openaiRequestLogMapper;

    // ==================== 日志记录功能 ====================

    /**
     * 创建请求日志信息对象
     */
    public OpenAiRequestLogInfo createRequestLog() {
        return OpenAiRequestLogInfo.builder()
                .requestId(generateRequestId())
                .startTime(LocalDateTime.now())
                .retryCount(0)
                .build();
    }

    /**
     * 记录请求开始
     */
    public void logRequestStart(OpenAiRequestLogInfo logInfo, String requestType, String modelName, Long userId) {
        logInfo.setStartTime(LocalDateTime.now());
        logInfo.setRequestType(requestType);
        logInfo.setModelName(modelName);
        logInfo.setUserId(userId);

        log.info("[请求开始] ID: {} | 类型: {} | 模型: {} | 用户: {} | 时间: {}",
                logInfo.getRequestId(), requestType, modelName, userId, logInfo.getStartTime());
    }

    /**
     * 记录请求成功
     */
    public void logRequestSuccess(OpenAiRequestLogInfo logInfo, Integer statusCode) {
        logInfo.setEndTime(LocalDateTime.now());
        logInfo.setSuccess(statusCode);
        logInfo.calculateDuration();

        log.info(logInfo.toLogString());

        // 自动保存到数据库
        saveToDatabase(logInfo);
    }

    /**
     * 记录请求失败
     */
    public void logRequestFailure(OpenAiRequestLogInfo logInfo, Integer statusCode, String errorMessage) {
        logInfo.setEndTime(LocalDateTime.now());
        logInfo.setFailure(statusCode, errorMessage);
        logInfo.calculateDuration();

        log.error(logInfo.toLogString());

        // 自动保存到数据库
        saveToDatabase(logInfo);
    }

    /**
     * 记录重试
     */
    public void logRetry(OpenAiRequestLogInfo logInfo, String reason) {
        logInfo.setRetryCount(logInfo.getRetryCount() + 1);
        log.warn("[请求重试] ID: {} | 重试次数: {} | 原因: {}",
                logInfo.getRequestId(), logInfo.getRetryCount(), reason);
    }

    /**
     * 设置提供商信息
     */
    public void setProviderInfo(OpenAiRequestLogInfo logInfo, String provider, String groupName,
                                String maskedApiKey, String actualBaseUrl, String pathSuffix) {
        logInfo.setProvider(provider);
        logInfo.setGroupName(groupName);
        logInfo.setMaskedApiKey(maskedApiKey);
        logInfo.setActualBaseUrl(actualBaseUrl);
        logInfo.setPathSuffix(pathSuffix);
    }

    /**
     * 记录详细的调试信息
     */
    public void logDebugInfo(OpenAiRequestLogInfo logInfo, String message) {
        log.debug("[请求调试] ID: {} | {}", logInfo.getRequestId(), message);
    }

    /**
     * 记录性能指标
     */
    public void logPerformanceMetrics(OpenAiRequestLogInfo logInfo) {
        if (logInfo.getDurationMs() != null) {
            if (logInfo.getDurationMs() > 10000) { // 超过10秒
                log.warn("[性能警告] ID: {} | 请求耗时过长: {}ms",
                        logInfo.getRequestId(), logInfo.getDurationMs());
            } else if (logInfo.getDurationMs() > 5000) { // 超过5秒
                log.info("[性能提醒] ID: {} | 请求耗时较长: {}ms",
                        logInfo.getRequestId(), logInfo.getDurationMs());
            }
        }

        if (logInfo.getRetryCount() > 0) {
            log.info("[重试统计] ID: {} | 总重试次数: {}",
                    logInfo.getRequestId(), logInfo.getRetryCount());
        }
    }

    // ==================== 查询功能 ====================

    /**
     * 分页查询请求日志
     *
     * @param queryRequest 查询请求参数
     * @return 分页结果
     */
    public PageFinalResult<OpenAiRequestLogResponse> queryRequestLogs(RequestLogQueryRequest queryRequest) {
        log.info("分页查询OpenAI请求日志 - 页码: {}, 大小: {}, 条件: {}",
                queryRequest.getPage(), queryRequest.getSize(), queryRequest);

        // 计算分页参数
        int offset = (queryRequest.getPage() - 1) * queryRequest.getSize();
        int limit = queryRequest.getSize();

        // 执行查询
        List<OpenAiRequestLog> records;
        long total;

        if (Boolean.TRUE.equals(queryRequest.getOnlyFailed())) {
            // 只查询失败的请求
            records = openaiRequestLogMapper.selectFailedRequestsPage(
                    offset, limit, queryRequest.getUserId(),
                    queryRequest.getStartTime(), queryRequest.getEndTime()
            );
            total = openaiRequestLogMapper.countFailedRequests(
                    queryRequest.getUserId(),
                    queryRequest.getStartTime(), queryRequest.getEndTime()
            );
        } else if (Boolean.TRUE.equals(queryRequest.getOnlySlow())) {
            // 只查询慢请求（默认5秒以上）
            Long minDuration = queryRequest.getMinDuration() != null ? queryRequest.getMinDuration() : 5000L;
            records = openaiRequestLogMapper.selectSlowRequestsPage(
                    offset, limit, minDuration, queryRequest.getUserId()
            );
            total = openaiRequestLogMapper.countSlowRequests(
                    minDuration, queryRequest.getUserId()
            );
        } else {
            // 普通分页查询
            records = openaiRequestLogMapper.selectRequestLogsPage(
                    offset, limit,
                    queryRequest.getUserId(),
                    queryRequest.getProvider(),
                    queryRequest.getStatus(),
                    queryRequest.getRequestType(),
                    queryRequest.getModelName(),
                    queryRequest.getStartTime(),
                    queryRequest.getEndTime()
            );
            total = openaiRequestLogMapper.countRequestLogs(
                    queryRequest.getUserId(),
                    queryRequest.getProvider(),
                    queryRequest.getStatus(),
                    queryRequest.getRequestType(),
                    queryRequest.getModelName(),
                    queryRequest.getStartTime(),
                    queryRequest.getEndTime()
            );
        }

        // 转换为响应DTO
        List<OpenAiRequestLogResponse> responseList = records.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // 构建分页结果
        PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.of(
                queryRequest.getPage(), queryRequest.getSize(), total
        );

        return new PageFinalResult<>(responseList, pageResult);
    }

    /**
     * 根据请求ID查询单个日志
     *
     * @param requestId 请求ID
     * @return 日志详情
     */
    public OpenAiRequestLogResponse getRequestLogByRequestId(String requestId) {
        log.info("查询单个OpenAI请求日志 - 请求ID: {}", requestId);

        OpenAiRequestLog requestLog = openaiRequestLogMapper.selectByRequestId(requestId);
        if (requestLog == null) {
            return null;
        }

        return convertToResponse(requestLog);
    }

    /**
     * 根据用户ID查询日志列表
     *
     * @param userId 用户ID
     * @return 日志列表
     */
    public List<OpenAiRequestLogResponse> getRequestLogsByUserId(Long userId) {
        log.info("查询用户OpenAI请求日志 - 用户ID: {}", userId);

        List<OpenAiRequestLog> requestLogs = openaiRequestLogMapper.selectByUserId(userId);
        return requestLogs.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID删除请求日志
     *
     * @param id 日志ID
     * @return 是否删除成功
     */
    public boolean deleteRequestLogById(Long id) {
        log.info("删除OpenAI请求日志 - 日志ID: {}", id);

        try {
            int affectedRows = openaiRequestLogMapper.deleteById(id);
            if (affectedRows > 0) {
                log.info("成功删除OpenAI请求日志 - 日志ID: {}", id);
                return true;
            } else {
                log.warn("删除OpenAI请求日志失败，日志不存在 - 日志ID: {}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("删除OpenAI请求日志异常 - 日志ID: {} | 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "req_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 保存日志信息到数据库
     */
    private void saveToDatabase(OpenAiRequestLogInfo logInfo) {
        try {
            OpenAiRequestLog requestLog = convertToEntity(logInfo);
            openaiRequestLogMapper.insert(requestLog);
            log.debug("[数据库保存] OpenAI请求日志已保存 - ID: {}", logInfo.getRequestId());
        } catch (Exception e) {
            log.error("[数据库保存失败] 请求ID: {} | 错误: {}", logInfo.getRequestId(), e.getMessage(), e);
            // 数据库保存失败不应该影响主流程，只记录错误日志
        }
    }

    /**
     * 将RequestLogInfo转换为RequestLog实体
     */
    private OpenAiRequestLog convertToEntity(OpenAiRequestLogInfo logInfo) {
        return OpenAiRequestLog.builder()
                .requestId(logInfo.getRequestId())
                .userId(logInfo.getUserId())
                .startTime(logInfo.getStartTime())
                .endTime(logInfo.getEndTime())
                .status(logInfo.getStatus())
                .statusCode(logInfo.getStatusCode())
                .requestType(logInfo.getRequestType())
                .durationMs(logInfo.getDurationMs())
                .retryCount(logInfo.getRetryCount())
                .groupName(logInfo.getGroupName())
                .provider(logInfo.getProvider())
                .maskedApiKey(logInfo.getMaskedApiKey())
                .errorMessage(logInfo.getErrorMessage())
                .actualBaseUrl(logInfo.getActualBaseUrl())
                .pathSuffix(logInfo.getPathSuffix())
                .modelName(logInfo.getModelName())
                .extraInfo(logInfo.getExtraInfo())
                .build();
    }

    /**
     * 将RequestLog实体转换为响应DTO
     */
    private OpenAiRequestLogResponse convertToResponse(OpenAiRequestLog requestLog) {
        return OpenAiRequestLogResponse.builder()
                .id(requestLog.getId())
                .requestId(requestLog.getRequestId())
                .userId(requestLog.getUserId())
                .startTime(requestLog.getStartTime())
                .endTime(requestLog.getEndTime())
                .status(requestLog.getStatus())
                .statusCode(requestLog.getStatusCode())
                .requestType(requestLog.getRequestType())
                .durationMs(requestLog.getDurationMs())
                .retryCount(requestLog.getRetryCount())
                .groupName(requestLog.getGroupName())
                .provider(requestLog.getProvider())
                .maskedApiKey(requestLog.getMaskedApiKey())
                .errorMessage(requestLog.getErrorMessage())
                .actualBaseUrl(requestLog.getActualBaseUrl())
                .pathSuffix(requestLog.getPathSuffix())
                .modelName(requestLog.getModelName())
                .extraInfo(requestLog.getExtraInfo())
                .createdAt(requestLog.getCreatedAt())
                .build();
    }
}
