package com.example.pure.service.log;

import com.example.pure.mapper.primary.RequestLogMapper;
import com.example.pure.model.dto.log.RequestLogInfo;
import com.example.pure.model.entity.RequestLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 请求日志服务
 * <p>
 * 负责记录API请求的详细日志信息，并自动保存到数据库
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequestLogService {

    private final RequestLogMapper requestLogMapper;

    /**
     * 创建请求日志信息对象
     */
    public RequestLogInfo createRequestLog() {
        return RequestLogInfo.builder()
                .requestId(generateRequestId())
                .startTime(LocalDateTime.now())
                .retryCount(0)
                .build();
    }

    /**
     * 记录请求开始
     */
    public void logRequestStart(RequestLogInfo logInfo, String requestType, String modelName, Long userId) {
        logInfo.setStartTime(LocalDateTime.now());
        logInfo.setRequestType(requestType);
        logInfo.setModelName(modelName);
        logInfo.setUserId(userId);
        
        log.info("[请求开始] ID: {} | 类型: {} | 模型: {} | 用户: {} | 时间: {}", 
                logInfo.getRequestId(), requestType, modelName, userId, logInfo.getStartTime());
    }

    /**
     * 记录请求成功
     */
    public void logRequestSuccess(RequestLogInfo logInfo, Integer statusCode) {
        logInfo.setEndTime(LocalDateTime.now());
        logInfo.setSuccess(statusCode);
        logInfo.calculateDuration();

        log.info(logInfo.toLogString());

        // 自动保存到数据库
        saveToDatabase(logInfo);
    }

    /**
     * 记录请求失败
     */
    public void logRequestFailure(RequestLogInfo logInfo, Integer statusCode, String errorMessage) {
        logInfo.setEndTime(LocalDateTime.now());
        logInfo.setFailure(statusCode, errorMessage);
        logInfo.calculateDuration();

        log.error(logInfo.toLogString());

        // 自动保存到数据库
        saveToDatabase(logInfo);
    }

    /**
     * 记录重试
     */
    public void logRetry(RequestLogInfo logInfo, String reason) {
        logInfo.setRetryCount(logInfo.getRetryCount() + 1);
        log.warn("[请求重试] ID: {} | 重试次数: {} | 原因: {}", 
                logInfo.getRequestId(), logInfo.getRetryCount(), reason);
    }

    /**
     * 设置提供商信息
     */
    public void setProviderInfo(RequestLogInfo logInfo, String provider, String groupName, 
                               String maskedApiKey, String actualBaseUrl, String pathSuffix) {
        logInfo.setProvider(provider);
        logInfo.setGroupName(groupName);
        logInfo.setMaskedApiKey(maskedApiKey);
        logInfo.setActualBaseUrl(actualBaseUrl);
        logInfo.setPathSuffix(pathSuffix);
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "req_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 记录详细的调试信息
     */
    public void logDebugInfo(RequestLogInfo logInfo, String message) {
        log.debug("[请求调试] ID: {} | {}", logInfo.getRequestId(), message);
    }

    /**
     * 记录性能指标
     */
    public void logPerformanceMetrics(RequestLogInfo logInfo) {
        if (logInfo.getDurationMs() != null) {
            if (logInfo.getDurationMs() > 10000) { // 超过10秒
                log.warn("[性能警告] ID: {} | 请求耗时过长: {}ms",
                        logInfo.getRequestId(), logInfo.getDurationMs());
            } else if (logInfo.getDurationMs() > 5000) { // 超过5秒
                log.info("[性能提醒] ID: {} | 请求耗时较长: {}ms",
                        logInfo.getRequestId(), logInfo.getDurationMs());
            }
        }

        if (logInfo.getRetryCount() > 0) {
            log.info("[重试统计] ID: {} | 总重试次数: {}",
                    logInfo.getRequestId(), logInfo.getRetryCount());
        }
    }

    /**
     * 保存日志信息到数据库
     */
    private void saveToDatabase(RequestLogInfo logInfo) {
        try {
            RequestLog requestLog = convertToEntity(logInfo);
            requestLogMapper.insert(requestLog);
            log.debug("[数据库保存] 请求日志已保存 - ID: {}", logInfo.getRequestId());
        } catch (Exception e) {
            log.error("[数据库保存失败] 请求ID: {} | 错误: {}", logInfo.getRequestId(), e.getMessage(), e);
            // 数据库保存失败不应该影响主流程，只记录错误日志
        }
    }

    /**
     * 将RequestLogInfo转换为RequestLog实体
     */
    private RequestLog convertToEntity(RequestLogInfo logInfo) {
        return RequestLog.builder()
                .requestId(logInfo.getRequestId())
                .userId(logInfo.getUserId())
                .startTime(logInfo.getStartTime())
                .endTime(logInfo.getEndTime())
                .status(logInfo.getStatus())
                .statusCode(logInfo.getStatusCode())
                .requestType(logInfo.getRequestType())
                .durationMs(logInfo.getDurationMs())
                .retryCount(logInfo.getRetryCount())
                .groupName(logInfo.getGroupName())
                .provider(logInfo.getProvider())
                .maskedApiKey(logInfo.getMaskedApiKey())
                .errorMessage(logInfo.getErrorMessage())
                .actualBaseUrl(logInfo.getActualBaseUrl())
                .pathSuffix(logInfo.getPathSuffix())
                .modelName(logInfo.getModelName())
                .extraInfo(logInfo.getExtraInfo())
                .isDeleted(false)
                .build();
    }
}
