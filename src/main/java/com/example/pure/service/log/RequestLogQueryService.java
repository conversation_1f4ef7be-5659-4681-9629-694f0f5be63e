package com.example.pure.service.log;

import com.example.pure.common.PageFinalResult;
import com.example.pure.mapper.primary.RequestLogMapper;
import com.example.pure.model.dto.request.log.RequestLogQueryRequest;
import com.example.pure.model.dto.response.log.RequestLogResponse;
import com.example.pure.model.entity.RequestLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 请求日志查询服务
 * <p>
 * 负责处理请求日志的查询和分页功能
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RequestLogQueryService {

    private final RequestLogMapper requestLogMapper;

    /**
     * 分页查询请求日志
     *
     * @param queryRequest 查询请求参数
     * @return 分页结果
     */
    public PageFinalResult<RequestLogResponse> queryRequestLogs(RequestLogQueryRequest queryRequest) {
        log.info("分页查询请求日志 - 页码: {}, 大小: {}, 条件: {}",
                queryRequest.getPage(), queryRequest.getSize(), queryRequest);

        // 计算分页参数
        int offset = (queryRequest.getPage() - 1) * queryRequest.getSize();
        int limit = queryRequest.getSize();

        // 执行查询
        List<RequestLog> records;
        long total;

        if (Boolean.TRUE.equals(queryRequest.getOnlyFailed())) {
            // 只查询失败的请求
            records = requestLogMapper.selectFailedRequestsPage(
                    offset, limit, queryRequest.getUserId(),
                    queryRequest.getStartTime(), queryRequest.getEndTime()
            );
            total = requestLogMapper.countFailedRequests(
                    queryRequest.getUserId(),
                    queryRequest.getStartTime(), queryRequest.getEndTime()
            );
        } else if (Boolean.TRUE.equals(queryRequest.getOnlySlow())) {
            // 只查询慢请求（默认5秒以上）
            Long minDuration = queryRequest.getMinDuration() != null ? queryRequest.getMinDuration() : 5000L;
            records = requestLogMapper.selectSlowRequestsPage(
                    offset, limit, minDuration, queryRequest.getUserId()
            );
            total = requestLogMapper.countSlowRequests(
                    minDuration, queryRequest.getUserId()
            );
        } else {
            // 普通分页查询
            records = requestLogMapper.selectRequestLogsPage(
                    offset, limit,
                    queryRequest.getUserId(),
                    queryRequest.getProvider(),
                    queryRequest.getStatus(),
                    queryRequest.getRequestType(),
                    queryRequest.getModelName(),
                    queryRequest.getStartTime(),
                    queryRequest.getEndTime()
            );
            total = requestLogMapper.countRequestLogs(
                    queryRequest.getUserId(),
                    queryRequest.getProvider(),
                    queryRequest.getStatus(),
                    queryRequest.getRequestType(),
                    queryRequest.getModelName(),
                    queryRequest.getStartTime(),
                    queryRequest.getEndTime()
            );
        }

        // 转换为响应DTO
        List<RequestLogResponse> responseList = records.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // 计算总页数
        int pages = (int) Math.ceil((double) total / queryRequest.getSize());

        // 构建分页结果
        PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.of(
                queryRequest.getPage(), queryRequest.getSize(), total
        );

        return new PageFinalResult<>(responseList, pageResult);
    }

    /**
     * 根据请求ID查询单个日志
     *
     * @param requestId 请求ID
     * @return 日志详情
     */
    public RequestLogResponse getRequestLogByRequestId(String requestId) {
        log.info("查询单个请求日志 - 请求ID: {}", requestId);
        
        RequestLog requestLog = requestLogMapper.selectByRequestId(requestId);
        if (requestLog == null) {
            return null;
        }
        
        return convertToResponse(requestLog);
    }

    /**
     * 将RequestLog实体转换为响应DTO
     */
    private RequestLogResponse convertToResponse(RequestLog requestLog) {
        return RequestLogResponse.builder()
                .id(requestLog.getId())
                .requestId(requestLog.getRequestId())
                .userId(requestLog.getUserId())
                .startTime(requestLog.getStartTime())
                .endTime(requestLog.getEndTime())
                .status(requestLog.getStatus())
                .statusCode(requestLog.getStatusCode())
                .requestType(requestLog.getRequestType())
                .durationMs(requestLog.getDurationMs())
                .retryCount(requestLog.getRetryCount())
                .groupName(requestLog.getGroupName())
                .provider(requestLog.getProvider())
                .maskedApiKey(requestLog.getMaskedApiKey())
                .errorMessage(requestLog.getErrorMessage())
                .actualBaseUrl(requestLog.getActualBaseUrl())
                .pathSuffix(requestLog.getPathSuffix())
                .modelName(requestLog.getModelName())
                .extraInfo(requestLog.getExtraInfo())
                .createdAt(requestLog.getCreatedAt())
                .build();
    }
}
