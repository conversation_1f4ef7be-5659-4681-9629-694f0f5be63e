package com.example.pure.annotation;

/**
 * 数据脱敏策略枚举
 * <p>
 * 定义了不同类型敏感数据的脱敏策略，配合 {@link DataMasking} 注解使用。
 * </p>
 */
public enum MaskingStrategy {
    NONE,       // 不脱敏
    PASSWORD,   // 密码脱敏
    EMAIL,      // 邮箱脱敏
    USERNAME,   // 用户名脱敏
    PHONE,      // 手机号脱敏
    OPENAI_API_KEY,  // OpenAI API Key 脱敏
    ID_CARD,    // 身份证号脱敏
    ADDRESS,    // 地址脱敏
    BANK_CARD,  // 银行卡号脱敏
    DEFAULT     // 默认脱敏策略
}
