package com.example.pure.model.entity;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 请求日志实体类
 * <p>
 * 用于存储API请求的详细日志信息到数据库
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestLog {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 请求ID（用于追踪）
     */
    private String requestId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 请求开始时间
     */
    private LocalDateTime startTime;

    /**
     * 请求结束时间
     */
    private LocalDateTime endTime;

    /**
     * 请求状态（成功/失败）
     */
    private String status;

    /**
     * HTTP状态码
     */
    private Integer statusCode;

    /**
     * 请求类型（stream/non-stream）
     */
    private String requestType;

    /**
     * 花费时间（毫秒）
     */
    private Long durationMs;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 配置分组名称
     */
    private String groupName;

    /**
     * AI提供商
     */
    private String provider;

    /**
     * 使用的API密钥（脱敏）
     */
    private String maskedApiKey;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * WebClient请求的实际AI大模型基础路径
     */
    private String actualBaseUrl;

    /**
     * 请求路径后缀
     */
    private String pathSuffix;

    /**
     * 请求的模型名称
     */
    private String modelName;

    /**
     * 额外信息（JSON格式）
     */
    private String extraInfo;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
