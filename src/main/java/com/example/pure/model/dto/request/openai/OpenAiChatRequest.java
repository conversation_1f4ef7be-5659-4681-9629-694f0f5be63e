package com.example.pure.model.dto.request.openai;

import com.example.pure.config.ContentDeserializer;
import com.example.pure.model.dto.request.ValidatableRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * OpenAI聊天完成请求DTO
 * <p>
 * 完全兼容OpenAI API格式的聊天请求
 * </p>
 */
@Data
@Builder
@Schema(description = "OpenAI聊天完成请求")
@JsonIgnoreProperties(ignoreUnknown = true) // 忽略多余未知的变量
public class OpenAiChatRequest implements ValidatableRequest {

    /**
     * 使用的模型ID
     */
    @NotBlank(message = "模型不能为空")
    @Schema(description = "模型ID", example = "gpt-3.5-turbo", required = true)
    private String model;

    /**
     * 消息列表
     */
    @NotEmpty(message = "消息列表不能为空")
    @Schema(description = "消息列表", required = true)
    private List<OpenAiMessage> messages;

    /**
     * 温度参数，控制随机性
     */
    @DecimalMin(value = "0.0", message = "温度参数不能小于0")
    @DecimalMax(value = "2.0", message = "温度参数不能大于2")
    @Schema(description = "温度参数，控制随机性", example = "0.7", minimum = "0", maximum = "2")
    private Double temperature;

    /**
     * 最大生成token数
     */
    @Min(value = 1, message = "最大token数不能小于1")
    @Max(value = 32768, message = "最大token数不能大于32768")
    @JsonProperty("max_tokens")
    @Schema(description = "最大生成token数", example = "2048")
    private Integer maxTokens;

    /**
     * 核采样参数
     */
    @DecimalMin(value = "0.0", message = "top_p参数不能小于0")
    @DecimalMax(value = "1.0", message = "top_p参数不能大于1")
    @JsonProperty("top_p")
    @Schema(description = "核采样参数", example = "1.0", minimum = "0", maximum = "1")
    private Double topP;

    /**
     * 是否启用流式响应
     */
    @Schema(description = "是否启用流式响应", example = "true")
    private Boolean stream = false;

    /**
     * 停止序列
     */
    @Schema(description = "停止序列")
    private List<String> stop;

    /**
     * 存在惩罚
     */
    @DecimalMin(value = "-2.0", message = "存在惩罚不能小于-2")
    @DecimalMax(value = "2.0", message = "存在惩罚不能大于2")
    @JsonProperty("presence_penalty")
    @Schema(description = "存在惩罚", minimum = "-2", maximum = "2")
    private Double presencePenalty;

    /**
     * 频率惩罚
     */
    @DecimalMin(value = "-2.0", message = "频率惩罚不能小于-2")
    @DecimalMax(value = "2.0", message = "频率惩罚不能大于2")
    @JsonProperty("frequency_penalty")
    @Schema(description = "频率惩罚", minimum = "-2", maximum = "2")
    private Double frequencyPenalty;

    /**
     * 用户标识
     */
    @Schema(description = "用户标识")
    private String user;

    /**
     * 是否包含推理过程（兼容OpenAI o1格式）
     * <p>
     * 当设置为true时，响应将包含AI的思考过程
     * 兼容OpenAI o1系列模型的reasoning_content格式
     * </p>
     */
    @JsonProperty("include_reasoning") // 接收Json的include_reasoning的值转换到这个类的includeReasoning的变量里
    @Schema(description = "是否包含推理过程", example = "false")
    private Boolean includeReasoning = false;

    /**
     * 工具列表（Function Calling支持）
     * <p>
     * 支持OpenAI兼容的Function Calling格式
     * 每个工具包含type和function定义
     * </p>
     */
    @Schema(description = "工具列表，支持Function Calling")
    private List<Tool> tools;

    /**
     * 工具选择策略
     * <p>
     * 控制模型如何选择和使用工具：
     * - "none": 不使用任何工具
     * - "auto": 自动选择是否使用工具（默认）
     * - "required": 强制使用工具
     * - {"type": "function", "function": {"name": "function_name"}}: 强制使用指定工具
     * </p>
     */
    @JsonProperty("tool_choice")
    @Schema(description = "工具选择策略", example = "auto")
    private Object toolChoice;

    /**
     * 响应格式控制（JSON输出支持）
     * <p>
     * 控制AI响应的格式：
     * - null或{"type": "text"}: 普通文本响应（默认）
     * - {"type": "json_object"}: 强制返回有效的JSON对象
     * </p>
     */
    @JsonProperty("response_format")
    @Schema(description = "响应格式控制")
    private ResponseFormat responseFormat;

    /**
     * 流式响应选项
     * <p>
     * 控制流式响应的行为：
     * - {"include_usage": true}: 在流式响应中包含token使用统计
     * </p>
     */
    @JsonProperty("stream_options")
    @Schema(description = "流式响应选项")
    private StreamOptions streamOptions;


    /**
     * OpenAI消息格式（支持多模态）
     */
    @Data
    @Schema(description = "OpenAI消息")
    public static class OpenAiMessage {
        /**
         * 消息角色
         */
        @NotBlank(message = "消息角色不能为空")
        @Schema(description = "消息角色", example = "user", allowableValues = {"system", "user", "assistant"})
        private String role;

        /**
         * 消息内容（支持字符串和数组两种格式）
         * <p>
         * 支持格式：
         * 1. 字符串格式：content: "Hello, how are you?"
         * 2. 数组格式：content: [{"type": "text", "text": "Hello"}]
         * 3. 多模态格式：content: [{"type": "text", "text": "Hello"}, {"type": "image_url", "image_url": {...}}]
         * </p>
         */
        @JsonDeserialize(using = ContentDeserializer.class)
        @Schema(description = "消息内容，支持字符串或多模态数组格式")
        private List<ContentPart> content;

        /**
         * 消息名称（可选）
         */
        @Schema(description = "消息名称")
        private String name;

        /**
         * 历史调用工具消息
         * 工具调用列表（当角色为assistant且包含工具调用时使用）
         */
        @JsonProperty("tool_calls")
        @Schema(description = "工具调用列表")
        private List<ToolCall> toolCalls;
    }

    /**
     * 多模态内容部分
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "多模态内容部分")
    public static class ContentPart {
        /**
         * 内容类型,type在一个对象里只能搭配一个text或者搭配一个imageUrl来使用
         */
        @NotBlank(message = "内容类型不能为空")
        @Schema(description = "内容类型", example = "text", allowableValues = {"text", "image_url"})
        private String type;

        /**
         * 文本内容（当type="text"时使用）
         */
        @Schema(description = "文本内容", example = "描述这张图片：")
        private String text;

        /**
         * 图片URL信息（当type="image_url"时使用）
         */
        @JsonProperty("image_url")
        @Schema(description = "图片URL信息")
        private ImageUrl imageUrl;
    }

    /**
     * 图片URL信息，著名AI大模型接收图片格式有两种类型一种是Base64URL和HTTP类型的URL
     */
    @Data
    @Schema(description = "图片URL信息")
    public static class ImageUrl {
        /**
         * 图片URL或base64数据
         */
        @NotBlank(message = "图片URL不能为空")
        @Schema(description = "图片URL或base64数据",
                example = "https://example.com/image.jpg")
        private String url;

        /**
         * 图片处理详细程度
         */
        @Schema(description = "图片处理详细程度",
                example = "auto",
                allowableValues = {"low", "high", "auto"})
        private String detail = "auto";
    }

    // ========================
    // ValidatableRequest接口实现
    // ========================

    @Override
    public void validate() throws IllegalArgumentException {
        // 这里可以添加聊天请求的特定验证逻辑
        // 目前主要的验证通过@Valid注解在控制器层处理
        // 多模态内容验证将在控制器中单独处理
    }

    @Override
    public String getRequestType() {
        return "聊天完成";
    }

    @Override
    public String getLogInfo() {
        int messageCount = messages != null ? messages.size() : 0;
        String streamInfo = Boolean.TRUE.equals(stream) ? "流式" : "非流式";
        int toolCount = tools != null ? tools.size() : 0;
        String toolInfo = toolCount > 0 ? String.format(", 工具数: %d", toolCount) : "";
        return String.format("模型: %s, 消息数: %d, 类型: %s, 最大令牌: %s%s",
                model,
                messageCount,
                streamInfo,
                maxTokens != null ? maxTokens.toString() : "默认",
                toolInfo);
    }

    /**
     * 工具定义（Function Calling）- 2025年最新格式
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL) // 正常没这注解会把null值反序列化为null,有注解就只序列化非null值
    @Schema(description = "工具定义")
    public static class Tool {
        /**
         * 工具类型，目前只支持"function"
         */
        @NotBlank(message = "工具类型不能为空")
        @Schema(description = "工具类型", example = "function", allowableValues = {"function"})
        private String type = "function";

        /**
         * 函数名称
         */
        @Schema(description = "函数名称", example = "get_weather")
        private String name;

        /**
         * 函数描述
         */
        @Schema(description = "函数描述", example = "获取指定城市的天气信息")
        private String description;

        /**
         * 函数参数定义（JSON Schema格式）
         */
        @Schema(description = "函数参数定义，使用JSON Schema格式")
        private Object parameters;
    }

    /**
     * 函数定义
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "函数定义")
    public static class Function {
        /**
         * 函数名称
         */
        @NotBlank(message = "函数名称不能为空")
        @Schema(description = "函数名称", example = "get_weather", required = true)
        private String name;

        /**
         * 函数描述
         */
        @Schema(description = "函数描述", example = "获取指定城市的天气信息")
        private String description;

        /**
         * 函数参数定义（JSON Schema格式）
         */
        @Schema(description = "函数参数定义，使用JSON Schema格式")
        private Object parameters;
    }

    /**
     * 工具调用（在assistant消息中使用）,保存AI大模型之前调用过工具的调用信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "工具调用")
    public static class ToolCall {
        /**
         * 工具调用ID
         */
        @Schema(description = "工具调用ID", example = "call_abc123")
        private String id;

        /**
         * 工具类型，目前只支持"function"
         */
        @Schema(description = "工具类型", example = "function", allowableValues = {"function"})
        private String type = "function";

        /**
         * 函数调用信息
         */
        @Schema(description = "函数调用信息")
        private FunctionCall function;
    }

    /**
     * 函数调用信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "函数调用信息")
    public static class FunctionCall {
        /**
         * 函数名称
         */
        @Schema(description = "函数名称", example = "get_weather")
        private String name;

        /**
         * 函数参数（JSON字符串格式）
         */
        @Schema(description = "函数参数，JSON字符串格式", example = "{\"location\": \"北京\"}")
        private String arguments;
    }

    /**
     * 响应格式定义
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "响应格式定义")
    public static class ResponseFormat {
        /**
         * 格式类型
         */
        @Schema(description = "格式类型", example = "json_object", allowableValues = {"text", "json_object"})
        private String type;
    }

    /**
     * 流式响应选项
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "流式响应选项")
    public static class StreamOptions {
        /**
         * 是否在流式响应中包含token使用统计
         */
        @JsonProperty("include_usage")
        @Schema(description = "是否在流式响应中包含token使用统计", example = "true")
        private Boolean includeUsage;
    }
}
