package com.example.pure.model.dto.response.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 请求日志响应DTO
 * <p>
 * 用于返回给前端的日志信息
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestLogResponse {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 请求开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 请求结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 请求状态
     */
    private String status;

    /**
     * HTTP状态码
     */
    private Integer statusCode;

    /**
     * 请求类型
     */
    private String requestType;

    /**
     * 花费时间（毫秒）
     */
    private Long durationMs;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 配置分组名称
     */
    private String groupName;

    /**
     * AI提供商
     */
    private String provider;

    /**
     * 使用的API密钥（脱敏）
     */
    private String maskedApiKey;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 实际请求的BaseURL
     */
    private String actualBaseUrl;

    /**
     * 请求路径后缀
     */
    private String pathSuffix;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 额外信息
     */
    private String extraInfo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 格式化的耗时显示
     */
    public String getFormattedDuration() {
        if (durationMs == null) {
            return "未知";
        }
        if (durationMs < 1000) {
            return durationMs + "ms";
        } else if (durationMs < 60000) {
            return String.format("%.2fs", durationMs / 1000.0);
        } else {
            long minutes = durationMs / 60000;
            long seconds = (durationMs % 60000) / 1000;
            return String.format("%dm%ds", minutes, seconds);
        }
    }

    /**
     * 获取状态标签样式
     */
    public String getStatusTag() {
        if ("成功".equals(status)) {
            return "success";
        } else if ("失败".equals(status)) {
            return "error";
        } else {
            return "default";
        }
    }

    /**
     * 获取请求类型标签
     */
    public String getRequestTypeTag() {
        if ("stream".equals(requestType)) {
            return "流式";
        } else if ("non-stream".equals(requestType)) {
            return "非流式";
        } else {
            return requestType;
        }
    }

    /**
     * 判断是否为慢请求（超过5秒）
     */
    public boolean isSlowRequest() {
        return durationMs != null && durationMs > 5000;
    }

    /**
     * 判断是否有重试
     */
    public boolean hasRetry() {
        return retryCount != null && retryCount > 0;
    }
}
