package com.example.pure.model.dto.log;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 请求日志信息
 * <p>
 * 用于记录API请求的详细信息，包括时间、状态、性能指标等
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestLogInfo {

    /**
     * 请求开始时间
     */
    private LocalDateTime startTime;

    /**
     * 请求结束时间
     */
    private LocalDateTime endTime;

    /**
     * 请求状态（成功/失败）
     */
    private String status;

    /**
     * HTTP状态码
     */
    private Integer statusCode;

    /**
     * 请求类型（stream/non-stream）
     */
    private String requestType;

    /**
     * 花费时间（毫秒）
     */
    private Long durationMs;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 配置分组名称
     */
    private String groupName;

    /**
     * AI提供商
     */
    private String provider;

    /**
     * 使用的API密钥（脱敏）
     */
    private String maskedApiKey;

    /**
     * 错误信息（失败时）
     */
    private String errorMessage;

    /**
     * WebClient请求的实际AI大模型基础路径
     */
    private String actualBaseUrl;

    /**
     * 请求路径后缀
     */
    private String pathSuffix;

    /**
     * 请求的模型名称
     */
    private String modelName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 请求ID（用于追踪）
     */
    private String requestId;

    /**
     * 额外信息（JSON格式）
     */
    private String extraInfo;

    /**
     * 计算请求持续时间
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 设置成功状态
     */
    public void setSuccess(Integer statusCode) {
        this.status = "成功";
        this.statusCode = statusCode;
    }

    /**
     * 设置失败状态
     */
    public void setFailure(Integer statusCode, String errorMessage) {
        this.status = "失败";
        this.statusCode = statusCode;
        this.errorMessage = errorMessage;
    }

    /**
     * 脱敏API密钥
     */
    public static String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * 生成日志字符串
     */
    public String toLogString() {
        return String.format(
            "[请求日志] 时间: %s-%s | 状态: %s(%d) | 类型: %s | 耗时: %dms | 重试: %d次 | " +
            "分组: %s | 提供商: %s | 密钥: %s | BaseURL: %s | 路径: %s | 模型: %s%s",
            startTime, endTime, status, statusCode, requestType, durationMs, retryCount,
            groupName, provider, maskedApiKey, actualBaseUrl, pathSuffix, modelName,
            errorMessage != null ? " | 错误: " + errorMessage : ""
        );
    }
}
