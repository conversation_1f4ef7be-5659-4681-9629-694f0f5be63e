package com.example.pure.model.dto.response;

import com.example.pure.annotation.DataMasking;
import com.example.pure.annotation.MaskingStrategy;
import com.example.pure.serializer.GenericDataMaskingSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * 兼容API密钥DTO
 * <p>
 * 用于兼容API密钥的传输和展示
 * </p>
 */
@Data
@Schema(description = "兼容API密钥信息")
public class CompatibleApiKeyDto {

    /**
     * 密钥ID
     */
    @Schema(description = "密钥ID", example = "1")
    private Long id;

    /**
     * 密钥名称
     */
    @Schema(description = "密钥名称", example = "生产环境主密钥")
    private String keyName;

    /**
     * 兼容格式的API密钥
     */
    @Schema(description = "兼容格式的API密钥", example = "sk-****...****abcd")
    private String compatibleKey;

    /**
     * 密钥哈希值（用于标识）
     */
    @Schema(description = "密钥哈希值", example = "a1b2c3d4e5f6...")
    @JsonSerialize(using = GenericDataMaskingSerializer.class)// 应用通用 Serializer，并指定用户名策略
    @DataMasking(strategy = MaskingStrategy.PASSWORD)
    private String keyHash;

    /**
     * 使用次数统计
     */
    @Schema(description = "使用次数统计", example = "1250")
    private Long usageCount;

    /**
     * 最后使用时间
     */
    @Schema(description = "最后使用时间")
    private Instant lastUsedAt;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Instant updatedAt;
}
