// src/main/java/com/example/demo13/controller/AuthController.java
package com.example.pure.controller.auth;

import com.example.pure.common.Result;
import com.example.pure.exception.JwtAuthenticationException;
import com.example.pure.model.dto.request.auth.LoginRequest;
import com.example.pure.model.dto.request.auth.RefreshTokenRequest;
import com.example.pure.model.dto.response.auth.TokenResponse;
import com.example.pure.model.dto.response.user.UserDTO;
import com.example.pure.service.auth.AccessLogService;
import com.example.pure.service.auth.AuthService;
import com.example.pure.service.auth.CaptchaService;
import com.example.pure.service.auth.IpRateLimitService;
import com.example.pure.service.user.UserService;
import com.example.pure.util.CookieUtil;
import com.example.pure.util.IpUtil;
import com.example.pure.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClient;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/* 1.请求映射和路由
     使用注解（例如 Spring MVC 中的 @RequestMapping, @GetMapping, @PostMapping 等）来定义哪些 URL 路径和 HTTP 方法应该被路由到这个控制器的方法
   2.请求参数处理
     从 HTTP 请求中提取参数,进行基本的参数验证
   3.调用服务层处理主要逻辑
     控制器应该 委托 业务逻辑处理给服务层 (Service Layer)。
   4.响应构建
     根据服务层的处理结果，构建 HTTP 响应。
   5.基本的异常处理
     处理控制器层级可能发生的异常
   6.安全认证和授权
     进行 高级别的 授权检查，例如：检查用户是否具有访问特定资源的权限。检查用户角色或权限。 */

/**
 * 认证控制器
 * 控制器适合运行请求类参数验证、认证、返回类包装的逻辑
 * 处理用户注册、登录、刷新令牌等请求
 * HttpServletResponse 对象主要负责处理 HTTP 协议层面的细节，如设置状态码、头部（包括 Cookie）、写入响应体等。
 * Result 类主要负责封装应用程序的业务逻辑结果（如成功/失败消息、数据等），提供一个统一的格式给前端。
 * SpringDoc生成OpenAPI文档和SwaggerUI的界面给前端查看并测试API
 *
 * 为了生成更详细、更准确的 API 文档，建议在你的 Controller 和 Model 类中添加 SpringDoc 提供的注解。
 * @Tag: 用于对 Controller 进行分组和描述。
 * @Operation: 用于描述一个 API 操作（方法）。
 * @Parameter: 用于描述 API 操作的参数。
 * @ApiResponse: 用于描述 API 操作的响应。
 * @Schema: 用于描述 API 操作的请求体或响应体的结构。
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/auth")
@Tag(name="用户账号账号Api",description="用户账号相关和令牌相关Api")//SpringDoc，生成文档前端用
public class AuthController {

    private final AuthService authService;
    private final AccessLogService accessLogService;
    private final UserService userService;
    private final IpRateLimitService rateLimitService;
    private final CaptchaService captchaService;
    private final IpUtil ipUtil;
    private final CookieUtil cookieUtil;
    private final JwtUtil jwtUtil;
    private final WebClient directWebClient;
    private final WebClient proxyWebClient;

    @Autowired
    public AuthController(
            AuthService authService,
            AccessLogService accessLogService,
            UserService userService,
            IpRateLimitService ipRateLimitService,
            IpUtil ipUtil,
            CookieUtil cookieUtil,
            JwtUtil jwtUtil,
            @Qualifier("directWebClient") WebClient directWebClient,
            @Qualifier("proxyWebClient") WebClient proxyWebClient,
            CaptchaService captchaService) {
        this.authService = authService;
        this.accessLogService = accessLogService;
        this.userService = userService;
        this.rateLimitService = ipRateLimitService;
        this.ipUtil = ipUtil;
        this.cookieUtil = cookieUtil;
        this.jwtUtil = jwtUtil;
        this.directWebClient = directWebClient;
        this.proxyWebClient = proxyWebClient;
        this.captchaService = captchaService;
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录方法")
    @ApiResponse(responseCode = "200", description = "成功")
    public Result<TokenResponse> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest,
            @Parameter(description = "添加Token到Cookie", required = true)
            HttpServletResponse response) {
        String clientIp = ipUtil.getClientIp(httpRequest);
        rateLimitService.checkIpRateLimit(clientIp, "login");

        if (!Objects.equals(request.getRecaptchaToken(), "3")){
            // 1. 进行人机验证
            captchaService.captchaVerify(request.getRecaptchaToken());
        }


        log.info("用户登录: {}", request.getUsername());
        TokenResponse tokenResponse = authService.login(request);

        UserDTO user = userService.findUserByUsername(request.getUsername());
        //记录登陆天数
        accessLogService.logAccess(user.getId(), "LOGIN", clientIp);

        // 添加访问令牌和刷新令牌到Cookie
        cookieUtil.addTokenCookies(response, tokenResponse.getAccessToken(), tokenResponse.getRefreshToken());


        return Result.success("登录成功", tokenResponse);
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新Token", description = "账号刷新Token方法")
    @ApiResponse(responseCode = "200", description = "成功")
    public Result<TokenResponse> refreshToken(
            @RequestBody(required = false) RefreshTokenRequest request,
            HttpServletRequest httpRequest,
            HttpServletResponse response) {
        String clientIp = ipUtil.getClientIp(httpRequest);
        rateLimitService.checkIpRateLimit(clientIp, "refresh");

        // 优先从请求体获取刷新令牌，如果没有则从Cookie获取
        String refreshToken = null;
        if (request != null && StringUtils.hasText(request.getRefreshToken())) {
            refreshToken = request.getRefreshToken();
        } else {
            refreshToken = cookieUtil.getRefreshTokenFromCookies(httpRequest);
        }

        // 验证刷新令牌是否存在
        if (!StringUtils.hasText(refreshToken)) {
            throw JwtAuthenticationException.RefreshExpired();
        }

        log.info("刷新令牌");
        TokenResponse tokenResponse = authService.refreshToken(refreshToken);

        // 更新Cookie中的令牌
        cookieUtil.addTokenCookies(response, tokenResponse.getAccessToken(), tokenResponse.getRefreshToken());

        return Result.success("令牌刷新成功", tokenResponse);
    }

    /**
     * 检查令牌状态
     * 用于客户端检查当前令牌是否有效，是否即将过期
     */
    @PostMapping("/check-token")
    public Result<Map<String, Object>> checkToken(
            @RequestHeader(value = "Authorization", required = false) String authHeader,
            HttpServletRequest httpRequest) {
        String clientIp = ipUtil.getClientIp(httpRequest);
        rateLimitService.checkIpRateLimit(clientIp, "check-token");

        // 优先从Authorization头获取令牌，如果没有则从Cookie获取
        String token = null;
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            token = jwtUtil.TokenCheck(authHeader); // 去掉"Bearer "前缀
        } else {
            token = cookieUtil.getAccessTokenFromCookies(httpRequest);
        }

        // 验证令牌是否存在
        if (!StringUtils.hasText(token)) {
            throw JwtAuthenticationException.expired();
        }

        Map<String, Object> status = new HashMap<>();
        // 检查是否已经过期
        boolean isExpired = jwtUtil.isTokenExpired(token);
        // 检查是否即将过期（30分钟内）
        boolean isAboutToExpire = jwtUtil.isTokenAboutToExpire(token, 30);

        status.put("valid", !isExpired);
        status.put("expired", isExpired);
        status.put("aboutToExpire", isAboutToExpire);

        return Result.success("令牌状态检查完成", status);
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Result<Void> logout(
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            // 获取当前登录用户信息
            Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            String username = null;
            if (principal instanceof UserDetails) {
                username = ((UserDetails) principal).getUsername();
            } else {
                username = principal.toString();
            }

            // 获取token (从cookie或请求头)
            String token = cookieUtil.getAccessTokenFromCookies(request);
            if (token == null) {
                // 如果cookie中没有，尝试从请求头获取
                String authHeader = request.getHeader(HttpHeaders.AUTHORIZATION);
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    token = authHeader.substring(7); // 去掉"Bearer "前缀
                }
            }

            // 如果找到token和用户名，调用AuthService移除设备记录
            if (token != null && username != null) {
                log.debug("用户 {} 注销，移除设备记录", username);
                authService.logout(username, token);
            }

            // 清除Cookie中的令牌
            cookieUtil.clearTokenCookies(response);
            return Result.success("退出登录成功");
        } catch (Exception e) {
            log.error("退出登录失败: {}", e.getMessage());
            return Result.success("退出登录成功"); // 即使出错也返回成功，让用户体验更好
        }
    }
}

