package com.example.pure.controller.openai;

import com.example.pure.common.PageFinalResult;
import com.example.pure.model.dto.request.log.RequestLogQueryRequest;
import com.example.pure.model.dto.response.log.RequestLogResponse;
import com.example.pure.service.openai.OpenaiRequestLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * OpenAI请求日志控制器
 * <p>
 * 提供OpenAI请求日志的查询和管理功能
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/logs/openai-requests")
@RequiredArgsConstructor
public class OpenaiRequestLogController {

    private final OpenaiRequestLogService openaiRequestLogService;

    /**
     * 分页查询OpenAI请求日志
     *
     * @param queryRequest 查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getOpenaiRequestLogsPage(
            @Valid RequestLogQueryRequest queryRequest) {

        log.info("收到分页查询OpenAI请求日志请求 - 页码: {}, 大小: {}", queryRequest.getPage(), queryRequest.getSize());

        try {
            PageFinalResult<RequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("分页查询OpenAI请求日志失败", e);
            PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.of(
                    queryRequest.getPage(), queryRequest.getSize(), 0L
            );
            return ResponseEntity.status(500).body(
                new PageFinalResult<RequestLogResponse>(null, pageResult)
            );
        }
    }

    /**
     * 根据请求ID查询单个日志详情
     *
     * @param requestId 请求ID
     * @return 日志详情
     */
    @GetMapping("/{requestId}")
    public ResponseEntity<RequestLogResponse> getOpenaiRequestLogDetail(@PathVariable String requestId) {
        log.info("查询OpenAI请求日志详情 - 请求ID: {}", requestId);

        try {
            RequestLogResponse response = openaiRequestLogService.getRequestLogByRequestId(requestId);
            if (response == null) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("查询OpenAI请求日志详情失败 - 请求ID: {}", requestId, e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 查询失败的OpenAI请求日志
     *
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID（可选）
     * @return 分页结果
     */
    @GetMapping("/failed")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getFailedOpenaiRequestLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) Long userId) {

        log.info("查询失败OpenAI请求日志 - 页码: {}, 大小: {}, 用户ID: {}", page, size, userId);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setUserId(userId);
            queryRequest.setOnlyFailed(true);

            PageFinalResult<RequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询失败OpenAI请求日志失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 查询慢OpenAI请求日志（耗时超过指定时间）
     *
     * @param page 页码
     * @param size 每页大小
     * @param minDuration 最小耗时（毫秒，默认5000ms）
     * @param userId 用户ID（可选）
     * @return 分页结果
     */
    @GetMapping("/slow")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getSlowOpenaiRequestLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(defaultValue = "5000") Long minDuration,
            @RequestParam(required = false) Long userId) {

        log.info("查询慢OpenAI请求日志 - 页码: {}, 大小: {}, 最小耗时: {}ms, 用户ID: {}", page, size, minDuration, userId);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setUserId(userId);
            queryRequest.setMinDuration(minDuration);
            queryRequest.setOnlySlow(true);

            PageFinalResult<RequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询慢OpenAI请求日志失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 根据提供商查询OpenAI请求日志
     *
     * @param provider 提供商
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    @GetMapping("/provider/{provider}")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getOpenaiRequestLogsByProvider(
            @PathVariable String provider,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        log.info("根据提供商查询OpenAI请求日志 - 提供商: {}, 页码: {}, 大小: {}", provider, page, size);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setProvider(provider);

            PageFinalResult<RequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("根据提供商查询OpenAI请求日志失败 - 提供商: {}", provider, e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 根据模型名称查询OpenAI请求日志
     *
     * @param modelName 模型名称
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    @GetMapping("/model/{modelName}")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getOpenaiRequestLogsByModel(
            @PathVariable String modelName,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        log.info("根据模型名称查询OpenAI请求日志 - 模型: {}, 页码: {}, 大小: {}", modelName, page, size);

        try {
            RequestLogQueryRequest queryRequest = new RequestLogQueryRequest();
            queryRequest.setPage(page);
            queryRequest.setSize(size);
            queryRequest.setModelName(modelName);

            PageFinalResult<RequestLogResponse> result = openaiRequestLogService.queryRequestLogs(queryRequest);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("根据模型名称查询OpenAI请求日志失败 - 模型: {}", modelName, e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 根据ID删除OpenAI请求日志
     *
     * @param id 日志ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteOpenaiRequestLog(@PathVariable Long id) {
        log.info("收到删除OpenAI请求日志请求 - 日志ID: {}", id);

        try {
            boolean success = openaiRequestLogService.deleteRequestLogById(id);
            if (success) {
                log.info("成功删除OpenAI请求日志 - 日志ID: {}", id);
                return ResponseEntity.ok("删除成功");
            } else {
                log.warn("删除OpenAI请求日志失败，日志不存在 - 日志ID: {}", id);
                return ResponseEntity.status(404).body("日志不存在");
            }
        } catch (Exception e) {
            log.error("删除OpenAI请求日志异常 - 日志ID: {}", id, e);
            return ResponseEntity.status(500).body("删除失败: " + e.getMessage());
        }
    }
}
