import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

public class TestStartup {
    public static void main(String[] args) {
        try {
            System.out.println("Testing Spring Boot application startup...");

            // Set test configuration
            System.setProperty("spring.profiles.active", "test");
            System.setProperty("spring.main.web-application-type", "none");
            System.setProperty("logging.level.root", "ERROR");

            // Try to start application context
            ConfigurableApplicationContext context = SpringApplication.run(
                Class.forName("com.example.pure.PureApplication"),
                args
            );

            System.out.println("SUCCESS: Application started successfully! No circular dependency issues.");

            // Check if key beans are properly injected
            Object aiConfigService = context.getBean("aiConfigServiceImpl");
            Object openAiCompatibleService = context.getBean("openAiCompatibleServiceImpl");
            Object openAiResponseConverter = context.getBean("openAiResponseConverter");

            System.out.println("SUCCESS: Key beans injected successfully:");
            System.out.println("  - AiConfigServiceImpl: " + aiConfigService.getClass().getSimpleName());
            System.out.println("  - OpenAiCompatibleServiceImpl: " + openAiCompatibleService.getClass().getSimpleName());
            System.out.println("  - OpenAiResponseConverter: " + openAiResponseConverter.getClass().getSimpleName());

            context.close();
            System.out.println("SUCCESS: Test completed, circular dependency issue resolved!");

        } catch (Exception e) {
            System.err.println("FAILED: Application startup failed:");
            e.printStackTrace();
            System.exit(1);
        }
    }
}
