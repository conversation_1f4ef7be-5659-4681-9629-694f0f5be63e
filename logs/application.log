2025-08-03 00:06:41.309 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 00:06:41.310 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 00:06:41.310 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:06:41.392 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 00:06:41.402 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 00:06:41.402 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:06:41.402 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e85df2b]
2025-08-03 00:06:41.402 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@588789068 wrapping com.mysql.cj.jdbc.ConnectionImpl@acff301] will be managed by Spring
2025-08-03 00:06:41.402 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 00:06:41.402 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 00:06:41.405 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 00:06:41.405 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e85df2b]
2025-08-03 00:06:41.405 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 00:06:41.405 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e85df2b]
2025-08-03 00:06:41.405 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e85df2b]
2025-08-03 00:06:41.405 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e85df2b]
2025-08-03 00:06:41.408 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 00:06:41.408 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 00:06:41.408 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 00:06:41.408 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 00:06:41.409 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:06:41.410 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I, AIzaSyBADRs (truncated)...]
2025-08-03 00:06:41.413 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 3
2025-08-03 00:06:41.413 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:06:41.413 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377811ea]
2025-08-03 00:06:41.413 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@988494061 wrapping com.mysql.cj.jdbc.ConnectionImpl@acff301] will be managed by Spring
2025-08-03 00:06:41.414 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 00:06:41.414 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 00:06:41.417 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 00:06:41.417 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377811ea]
2025-08-03 00:06:41.417 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:06:41.417 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:06:41.417 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:06:41.417 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:41.417 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 00:06:41.417 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=10, temperature=0.1, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:06:41.417 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:41.419 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.r.f.client.ExchangeFunctions : [16cab0c0] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:41.645 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] org.springframework.web.HttpLogging : [16cab0c0] Encoding [{stream=false, max_tokens=10, temperature=0.1, messages=[{role=user, content=Hi}], model=gemini-2.5- (truncated)...]
2025-08-03 00:06:42.099 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] o.s.w.r.f.client.ExchangeFunctions : [16cab0c0] [81eef680-1] Response 200 OK
2025-08-03 00:06:42.331 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] org.springframework.web.HttpLogging : [16cab0c0] [81eef680-1] Decoded "{"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":175415080 (truncated)..."
2025-08-03 00:06:42.331 [34mINFO [0;39m 20368 --- [reactor-http-nio-4] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:06:42.331 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":1754150802,"id":"kjeOaJOmBLnwqtsPxo7gyQ0","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":0,"prompt_tokens":2,"total_tokens":11}}
2025-08-03 00:06:42.331 [31mWARN [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:06:42.331 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:06:42.331 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:06:42.331 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:06:42.331 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:42.331 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBADR...
2025-08-03 00:06:42.331 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=10, temperature=0.1, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:06:42.332 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:42.332 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.r.f.client.ExchangeFunctions : [6058c31e] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:42.333 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] org.springframework.web.HttpLogging : [6058c31e] Encoding [{stream=false, max_tokens=10, temperature=0.1, messages=[{role=user, content=Hi}], model=gemini-2.5- (truncated)...]
2025-08-03 00:06:42.979 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] o.s.w.r.f.client.ExchangeFunctions : [6058c31e] [81eef680-2] Response 200 OK
2025-08-03 00:06:42.979 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] org.springframework.web.HttpLogging : [6058c31e] [81eef680-2] Decoded "{"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":175415080 (truncated)..."
2025-08-03 00:06:42.979 [34mINFO [0;39m 20368 --- [reactor-http-nio-4] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:06:42.979 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":1754150802,"id":"kjeOaNaTOqq9qtsPxKuOwAI","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":0,"prompt_tokens":2,"total_tokens":11}}
2025-08-03 00:06:42.980 [31mWARN [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:06:42.980 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:06:42.980 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:06:42.980 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:06:42.980 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:42.980 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-08-03 00:06:42.980 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=10, temperature=0.1, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:06:42.980 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:42.980 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.r.f.client.ExchangeFunctions : [79e3ad2e] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:06:42.981 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] org.springframework.web.HttpLogging : [79e3ad2e] Encoding [{stream=false, max_tokens=10, temperature=0.1, messages=[{role=user, content=Hi}], model=gemini-2.5- (truncated)...]
2025-08-03 00:06:43.556 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] o.s.w.r.f.client.ExchangeFunctions : [79e3ad2e] [81eef680-3] Response 200 OK
2025-08-03 00:06:43.609 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] org.springframework.web.HttpLogging : [79e3ad2e] [81eef680-3] Decoded "{"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":175415080 (truncated)..."
2025-08-03 00:06:43.609 [34mINFO [0;39m 20368 --- [reactor-http-nio-4] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:06:43.609 [39mDEBUG[0;39m 20368 --- [reactor-http-nio-4] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":1754150803,"id":"kzeOaKr7H_XMqtsPoriCsQg","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":0,"prompt_tokens":2,"total_tokens":11}}
2025-08-03 00:06:43.610 [31mWARN [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:06:43.610 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 3, 成功: 0, 失败: 3
2025-08-03 00:06:43.610 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377811ea]
2025-08-03 00:06:43.610 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377811ea]
2025-08-03 00:06:43.610 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@377811ea]
2025-08-03 00:06:43.630 [34mINFO [0;39m 20368 --- [http-nio-8080-exec-7] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 3, 成功: 0, 失败: 3
2025-08-03 00:06:43.631 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 00:06:43.632 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[], failedKeys= (truncated)...]
2025-08-03 00:06:43.633 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 00:06:43.633 [39mDEBUG[0;39m 20368 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 00:13:26.936 [39mDEBUG[0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 00:13:26.937 [39mDEBUG[0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 00:13:26.937 [34mINFO [0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-03 00:13:26.937 [34mINFO [0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4ed7fb78]]
2025-08-03 00:13:26.937 [39mDEBUG[0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4ed7fb78]
2025-08-03 00:13:26.937 [39mDEBUG[0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4ed7fb78]
2025-08-03 00:13:26.937 [34mINFO [0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-03 00:13:26.937 [39mDEBUG[0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:13:26.937 [39mDEBUG[0;39m 20368 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:13:29.196 [34mINFO [0;39m 20368 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-03 00:13:29.201 [34mINFO [0;39m 20368 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-03 00:13:34.104 [34mINFO [0;39m 1956 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-03 00:13:34.113 [34mINFO [0;39m 1956 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 1956 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-03 00:13:34.113 [39mDEBUG[0;39m 1956 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-03 00:13:34.114 [34mINFO [0;39m 1956 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-03 00:13:35.095 [34mINFO [0;39m 1956 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03 00:13:35.098 [34mINFO [0;39m 1956 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-03 00:13:35.148 [34mINFO [0;39m 1956 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OpenaiRequestLogMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-03 00:13:35.241 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-03 00:13:35.242 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-03 00:13:35.243 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-03 00:13:35.244 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-03 00:13:35.244 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-03 00:13:35.244 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-03 00:13:35.244 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-03 00:13:35.244 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-03 00:13:35.244 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'openAiRequestLogMapper' and 'com.example.pure.mapper.primary.OpenAiRequestLogMapper' mapperInterface
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'openAiRequestLogMapper'.
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-03 00:13:35.245 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-03 00:13:35.246 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-03 00:13:35.247 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-03 00:13:35.247 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-03 00:13:35.247 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-03 00:13:35.247 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-03 00:13:35.247 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-03 00:13:35.247 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-03 00:13:35.247 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-03 00:13:35.248 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-03 00:13:35.249 [39mDEBUG[0;39m 1956 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-03 00:13:35.858 [34mINFO [0;39m 1956 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-03 00:13:35.863 [34mINFO [0;39m 1956 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-03 00:13:35.864 [34mINFO [0;39m 1956 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-03 00:13:35.864 [34mINFO [0;39m 1956 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-03 00:13:35.965 [34mINFO [0;39m 1956 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-03 00:13:35.965 [34mINFO [0;39m 1956 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1791 ms
2025-08-03 00:13:36.241 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-03 00:13:36.252 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-03 00:13:36.262 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-03 00:13:36.270 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-03 00:13:36.279 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-03 00:13:36.283 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-03 00:13:36.288 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-03 00:13:36.297 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-03 00:13:36.301 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-03 00:13:36.306 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-03 00:13:36.311 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-03 00:13:36.324 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-03 00:13:36.331 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-03 00:13:36.338 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-03 00:13:36.344 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-03 00:13:36.352 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-03 00:13:36.357 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-03 00:13:36.361 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-03 00:13:36.366 [39mDEBUG[0;39m 1956 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-03 00:13:36.381 [34mINFO [0;39m 1956 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-03 00:13:36.675 [34mINFO [0;39m 1956 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-03 00:13:37.229 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-03 00:13:37.229 [39mDEBUG[0;39m 1956 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-03 00:13:37.536 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 00:13:37.538 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.042 [34mINFO [0;39m 1956 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-03 00:13:38.191 [34mINFO [0;39m 1956 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-03 00:13:38.201 [34mINFO [0;39m 1956 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-03 00:13:38.260 [34mINFO [0;39m 1956 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-03 00:13:38.260 [34mINFO [0;39m 1956 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-03 00:13:38.273 [34mINFO [0;39m 1956 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-03 00:13:38.304 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-03 00:13:38.305 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.306 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-03 00:13:38.306 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.306 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 00:13:38.306 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.306 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 00:13:38.306 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.307 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-03 00:13:38.307 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.307 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-03 00:13:38.307 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.311 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 00:13:38.312 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.312 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-03 00:13:38.312 [39mDEBUG[0;39m 1956 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-03 00:13:38.414 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-03 00:13:38.415 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-03 00:13:38.417 [34mINFO [0;39m 1956 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@234adbe2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2f154d9b, org.springframework.security.web.context.SecurityContextPersistenceFilter@42ceb67f, org.springframework.security.web.header.HeaderWriterFilter@1142843c, org.springframework.security.web.authentication.logout.LogoutFilter@57d1edf0, com.example.pure.filter.JwtFilter@48188d23, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2e1b1d22, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@653c0c9c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d8c264, org.springframework.security.web.session.SessionManagementFilter@5e2ab1c, org.springframework.security.web.access.ExceptionTranslationFilter@77a14911, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5f1eff72]
2025-08-03 00:13:38.419 [34mINFO [0;39m 1956 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-03 00:13:38.421 [34mINFO [0;39m 1956 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-03 00:13:38.421 [34mINFO [0;39m 1956 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-03 00:13:38.421 [34mINFO [0;39m 1956 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-03 00:13:38.592 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-03 00:13:38.608 [34mINFO [0;39m 1956 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-03 00:13:38.666 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 112 mappings in 'requestMappingHandlerMapping'
2025-08-03 00:13:38.674 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-03 00:13:39.018 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-03 00:13:39.123 [34mINFO [0;39m 1956 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-03 00:13:39.141 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-03 00:13:39.142 [39mDEBUG[0;39m 1956 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-03 00:13:39.143 [34mINFO [0;39m 1956 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7b80ac30, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@8d0253b, org.springframework.security.web.context.SecurityContextPersistenceFilter@2bdf6c7c, org.springframework.security.web.header.HeaderWriterFilter@52b2713a, org.springframework.web.filter.CorsFilter@f61f06e, org.springframework.security.web.authentication.logout.LogoutFilter@4ab959e8, com.example.pure.filter.JwtFilter@48188d23, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@13ac1657, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@52d68eb9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@437ac2dc, org.springframework.security.web.session.SessionManagementFilter@54e39504, org.springframework.security.web.access.ExceptionTranslationFilter@58ec04f4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@79f6b43a]
2025-08-03 00:13:39.176 [39mTRACE[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6de54b40, started on Sun Aug 03 00:13:34 CST 2025
2025-08-03 00:13:39.189 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-03 00:13:39.189 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-03 00:13:39.189 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-03 00:13:39.189 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-03 00:13:39.190 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiRequestLogController:
	
2025-08-03 00:13:39.191 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-03 00:13:39.194 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
2025-08-03 00:13:39.195 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-03 00:13:39.195 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-03 00:13:39.195 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-03 00:13:39.195 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-03 00:13:39.196 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-03 00:13:39.197 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-03 00:13:39.197 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-03 00:13:39.197 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-03 00:13:39.281 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-03 00:13:39.316 [39mDEBUG[0;39m 1956 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-03 00:13:39.527 [34mINFO [0;39m 1956 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-03 00:13:39.537 [34mINFO [0;39m 1956 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 00:13:39.539 [39mDEBUG[0;39m 1956 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 00:13:39.539 [39mDEBUG[0;39m 1956 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 00:13:39.539 [34mINFO [0;39m 1956 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-03 00:13:39.539 [39mDEBUG[0;39m 1956 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@f6e9c18]
2025-08-03 00:13:39.539 [39mDEBUG[0;39m 1956 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@f6e9c18]
2025-08-03 00:13:39.539 [34mINFO [0;39m 1956 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@f6e9c18]]
2025-08-03 00:13:39.539 [34mINFO [0;39m 1956 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-03 00:13:39.539 [39mDEBUG[0;39m 1956 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:13:39.539 [39mDEBUG[0;39m 1956 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:13:39.552 [34mINFO [0;39m 1956 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.961 seconds (JVM running for 6.475)
2025-08-03 00:13:50.940 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 00:13:50.940 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-08-03 00:13:50.940 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-08-03 00:13:50.941 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-08-03 00:13:50.941 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-08-03 00:13:50.942 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@64b3c652
2025-08-03 00:13:50.942 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@4348602f
2025-08-03 00:13:50.943 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-03 00:13:50.943 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-08-03 00:13:50.957 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 00:13:50.960 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 00:13:50.968 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:13:51.414 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 00:13:51.459 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 00:13:51.464 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:13:51.468 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fe221c3]
2025-08-03 00:13:51.473 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1503709165 wrapping com.mysql.cj.jdbc.ConnectionImpl@3c71803e] will be managed by Spring
2025-08-03 00:13:51.476 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 00:13:51.498 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 00:13:51.520 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 00:13:51.521 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fe221c3]
2025-08-03 00:13:51.522 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 00:13:51.522 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fe221c3]
2025-08-03 00:13:51.523 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fe221c3]
2025-08-03 00:13:51.523 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fe221c3]
2025-08-03 00:13:51.527 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 00:13:51.533 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 00:13:51.534 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 00:13:51.536 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 00:13:51.538 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:13:51.556 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I, AIzaSyBADRs (truncated)...]
2025-08-03 00:13:51.651 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 3
2025-08-03 00:13:51.653 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:13:51.653 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@114e5ddc]
2025-08-03 00:13:51.653 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@3c71803e] will be managed by Spring
2025-08-03 00:13:51.653 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 00:13:51.653 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 00:13:51.657 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 00:13:51.657 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@114e5ddc]
2025-08-03 00:13:51.658 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:13:51.659 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:13:51.660 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:13:51.661 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:51.661 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 00:13:51.661 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:13:51.693 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:51.720 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [626918e8] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:52.313 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [626918e8] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 00:13:52.956 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [626918e8] [b9ec39a0-1] Response 200 OK
2025-08-03 00:13:52.970 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [626918e8] [b9ec39a0-1] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you tod (truncated)..."
2025-08-03 00:13:52.970 [34mINFO [0;39m 1956 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:13:52.971 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754151232,"id":"QDmOaPv7N4S6qtsP44n5gAs","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":44}}
2025-08-03 00:13:52.971 [31mWARN [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:13:52.971 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:13:52.971 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:13:52.971 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:13:52.971 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:52.971 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBADR...
2025-08-03 00:13:52.971 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:13:52.971 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:52.972 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [6c5a8943] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:53.342 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [6c5a8943] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 00:13:54.104 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [6c5a8943] [635942f9-1] Response 200 OK
2025-08-03 00:13:54.106 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [6c5a8943] [635942f9-1] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hello! How can I help you today? (truncated)..."
2025-08-03 00:13:54.106 [34mINFO [0;39m 1956 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:13:54.106 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hello! How can I help you today?","role":"assistant"}}],"created":1754151234,"id":"QjmOaIryBPGKqtsP1ayb8QI","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":9,"prompt_tokens":2,"total_tokens":31}}
2025-08-03 00:13:54.106 [31mWARN [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:13:54.107 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:13:54.107 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:13:54.107 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:13:54.107 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:54.107 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-08-03 00:13:54.107 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:13:54.107 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:54.108 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [3ee0c843] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:13:54.109 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [3ee0c843] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 00:13:54.831 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [3ee0c843] [b9ec39a0-2] Response 200 OK
2025-08-03 00:13:54.833 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [3ee0c843] [b9ec39a0-2] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hello there! How can I help you  (truncated)..."
2025-08-03 00:13:54.833 [34mINFO [0;39m 1956 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:13:54.833 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hello there! How can I help you today?","role":"assistant"}}],"created":1754151234,"id":"QjmOaKK5MbvBqtsPu5KtgQM","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":36}}
2025-08-03 00:13:54.833 [31mWARN [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:13:54.833 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 3, 成功: 0, 失败: 3
2025-08-03 00:13:54.833 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@114e5ddc]
2025-08-03 00:13:54.833 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@114e5ddc]
2025-08-03 00:13:54.833 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@114e5ddc]
2025-08-03 00:13:54.836 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-2] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 3, 成功: 0, 失败: 3
2025-08-03 00:13:54.848 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 00:13:54.854 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[], failedKeys= (truncated)...]
2025-08-03 00:13:54.862 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 00:13:54.863 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 00:14:39.166 [34mINFO [0;39m 1956 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-03 00:16:19.041 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 00:16:19.041 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 00:16:19.042 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:16:19.122 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 00:16:19.126 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 00:16:19.126 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:16:19.126 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a646ff]
2025-08-03 00:16:19.126 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@2006380287 wrapping com.mysql.cj.jdbc.ConnectionImpl@3c71803e] will be managed by Spring
2025-08-03 00:16:19.126 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 00:16:19.127 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 00:16:19.129 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 00:16:19.129 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a646ff]
2025-08-03 00:16:19.129 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 00:16:19.129 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a646ff]
2025-08-03 00:16:19.129 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a646ff]
2025-08-03 00:16:19.129 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@29a646ff]
2025-08-03 00:16:19.132 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 00:16:19.132 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 00:16:19.132 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 00:16:19.132 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 00:16:19.133 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:16:19.133 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I], priority=1 (truncated)...]
2025-08-03 00:16:19.135 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 1
2025-08-03 00:16:19.135 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:16:19.135 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@689722c4]
2025-08-03 00:16:19.135 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@656703514 wrapping com.mysql.cj.jdbc.ConnectionImpl@3c71803e] will be managed by Spring
2025-08-03 00:16:19.135 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 00:16:19.136 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 00:16:19.138 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 00:16:19.138 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@689722c4]
2025-08-03 00:16:19.138 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:16:19.138 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:16:19.138 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:16:19.138 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:16:19.138 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 00:16:19.138 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:16:19.138 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:16:19.139 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.w.r.f.client.ExchangeFunctions : [77a741aa] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:16:19.140 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [77a741aa] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 00:16:19.829 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [77a741aa] [635942f9-2] Response 200 OK
2025-08-03 00:16:19.830 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [77a741aa] [635942f9-2] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hello! How can I help you today? (truncated)..."
2025-08-03 00:16:19.830 [34mINFO [0;39m 1956 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:16:19.830 [39mDEBUG[0;39m 1956 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hello! How can I help you today?","role":"assistant"}}],"created":1754151379,"id":"0zmOaLmRMaCUmtkP6qi-2AI","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":9,"prompt_tokens":2,"total_tokens":35}}
2025-08-03 00:16:19.831 [31mWARN [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:16:19.831 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 0, 失败: 1
2025-08-03 00:16:19.831 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@689722c4]
2025-08-03 00:16:19.831 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@689722c4]
2025-08-03 00:16:19.831 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@689722c4]
2025-08-03 00:16:19.833 [34mINFO [0;39m 1956 --- [http-nio-8080-exec-5] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 0, 失败: 1
2025-08-03 00:16:19.833 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 00:16:19.834 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[], failedKeys= (truncated)...]
2025-08-03 00:16:19.835 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 00:16:19.835 [39mDEBUG[0;39m 1956 --- [http-nio-8080-exec-5] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 00:18:08.620 [39mDEBUG[0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 00:18:08.620 [39mDEBUG[0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 00:18:08.620 [34mINFO [0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-03 00:18:08.620 [34mINFO [0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@f6e9c18]]
2025-08-03 00:18:08.620 [39mDEBUG[0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@f6e9c18]
2025-08-03 00:18:08.620 [39mDEBUG[0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@f6e9c18]
2025-08-03 00:18:08.620 [34mINFO [0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-03 00:18:08.620 [39mDEBUG[0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:18:08.620 [39mDEBUG[0;39m 1956 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:18:10.832 [34mINFO [0;39m 1956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-03 00:18:10.836 [34mINFO [0;39m 1956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-03 00:18:15.846 [34mINFO [0;39m 18864 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-03 00:18:15.852 [34mINFO [0;39m 18864 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 18864 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-03 00:18:15.852 [39mDEBUG[0;39m 18864 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-03 00:18:15.853 [34mINFO [0;39m 18864 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-03 00:18:16.754 [34mINFO [0;39m 18864 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03 00:18:16.756 [34mINFO [0;39m 18864 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-03 00:18:16.804 [34mINFO [0;39m 18864 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-08-03 00:18:16.917 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-03 00:18:16.917 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-03 00:18:16.917 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-03 00:18:16.917 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-03 00:18:16.917 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-03 00:18:16.917 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-03 00:18:16.917 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OpenaiRequestLogMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-03 00:18:16.918 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-03 00:18:16.919 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-03 00:18:16.921 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-03 00:18:16.921 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-03 00:18:16.921 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-03 00:18:16.921 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-03 00:18:16.921 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-03 00:18:16.922 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-03 00:18:16.922 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-03 00:18:16.922 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'openAiRequestLogMapper' and 'com.example.pure.mapper.primary.OpenAiRequestLogMapper' mapperInterface
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'openAiRequestLogMapper'.
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-03 00:18:16.923 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-03 00:18:16.924 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-03 00:18:16.924 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-03 00:18:16.924 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-03 00:18:16.924 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-03 00:18:16.924 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-03 00:18:16.924 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-03 00:18:16.925 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-03 00:18:16.925 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-03 00:18:16.925 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-03 00:18:16.925 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-03 00:18:16.925 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-03 00:18:16.925 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-03 00:18:16.925 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-03 00:18:16.926 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-03 00:18:16.926 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-03 00:18:16.926 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-03 00:18:16.926 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-03 00:18:16.926 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-03 00:18:16.927 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-03 00:18:16.927 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-03 00:18:16.927 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-03 00:18:16.927 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-03 00:18:16.927 [39mDEBUG[0;39m 18864 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-03 00:18:17.488 [34mINFO [0;39m 18864 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-03 00:18:17.494 [34mINFO [0;39m 18864 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-03 00:18:17.495 [34mINFO [0;39m 18864 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-03 00:18:17.495 [34mINFO [0;39m 18864 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-03 00:18:17.591 [34mINFO [0;39m 18864 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-03 00:18:17.591 [34mINFO [0;39m 18864 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1698 ms
2025-08-03 00:18:17.861 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-03 00:18:17.871 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-03 00:18:17.880 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-03 00:18:17.888 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-03 00:18:17.895 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-03 00:18:17.899 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-03 00:18:17.903 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-03 00:18:17.912 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-03 00:18:17.915 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-03 00:18:17.920 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-03 00:18:17.923 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-03 00:18:17.936 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-03 00:18:17.941 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-03 00:18:17.948 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-03 00:18:17.953 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-03 00:18:17.962 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-03 00:18:17.967 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-03 00:18:17.971 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-03 00:18:17.975 [39mDEBUG[0;39m 18864 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-03 00:18:17.988 [34mINFO [0;39m 18864 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-03 00:18:18.259 [34mINFO [0;39m 18864 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-03 00:18:18.767 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-03 00:18:18.767 [39mDEBUG[0;39m 18864 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-03 00:18:19.042 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 00:18:19.043 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.486 [34mINFO [0;39m 18864 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-03 00:18:19.622 [34mINFO [0;39m 18864 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-03 00:18:19.631 [34mINFO [0;39m 18864 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-03 00:18:19.688 [34mINFO [0;39m 18864 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-03 00:18:19.688 [34mINFO [0;39m 18864 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-03 00:18:19.701 [34mINFO [0;39m 18864 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-03 00:18:19.735 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 00:18:19.735 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.736 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-03 00:18:19.736 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.736 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 00:18:19.736 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.737 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-03 00:18:19.737 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.738 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-03 00:18:19.738 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.738 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-03 00:18:19.738 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.742 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 00:18:19.742 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.743 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-03 00:18:19.743 [39mDEBUG[0;39m 18864 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-03 00:18:19.847 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-03 00:18:19.847 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-03 00:18:19.850 [34mINFO [0;39m 18864 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@35c69e6b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@78e386a5, org.springframework.security.web.context.SecurityContextPersistenceFilter@77a14911, org.springframework.security.web.header.HeaderWriterFilter@72bef795, org.springframework.security.web.authentication.logout.LogoutFilter@5d7f4cbb, com.example.pure.filter.JwtFilter@6fb219dd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1142843c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3993cecb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6138ee0e, org.springframework.security.web.session.SessionManagementFilter@3b2317b7, org.springframework.security.web.access.ExceptionTranslationFilter@77d0a492, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ceeb592]
2025-08-03 00:18:19.852 [34mINFO [0;39m 18864 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-03 00:18:19.854 [34mINFO [0;39m 18864 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-03 00:18:19.854 [34mINFO [0;39m 18864 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-03 00:18:19.854 [34mINFO [0;39m 18864 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-03 00:18:20.022 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-03 00:18:20.038 [34mINFO [0;39m 18864 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-03 00:18:20.094 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 112 mappings in 'requestMappingHandlerMapping'
2025-08-03 00:18:20.100 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-03 00:18:20.421 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-03 00:18:20.529 [34mINFO [0;39m 18864 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-03 00:18:20.547 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-03 00:18:20.547 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-03 00:18:20.548 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-03 00:18:20.549 [39mDEBUG[0;39m 18864 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-03 00:18:20.549 [34mINFO [0;39m 18864 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@643947d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44b99f09, org.springframework.security.web.context.SecurityContextPersistenceFilter@74727e41, org.springframework.security.web.header.HeaderWriterFilter@51d50b5f, org.springframework.web.filter.CorsFilter@261d7ee2, org.springframework.security.web.authentication.logout.LogoutFilter@2c914364, com.example.pure.filter.JwtFilter@6fb219dd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@16117d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@78977b59, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@724a80d2, org.springframework.security.web.session.SessionManagementFilter@38816a6c, org.springframework.security.web.access.ExceptionTranslationFilter@242a3997, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@94ba90a]
2025-08-03 00:18:20.580 [39mTRACE[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@e6516e, started on Sun Aug 03 00:18:15 CST 2025
2025-08-03 00:18:20.593 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-03 00:18:20.594 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-03 00:18:20.595 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-03 00:18:20.595 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-03 00:18:20.595 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiRequestLogController:
	
2025-08-03 00:18:20.595 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-03 00:18:20.597 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-08-03 00:18:20.598 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-03 00:18:20.598 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-03 00:18:20.598 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-03 00:18:20.598 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-03 00:18:20.598 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-03 00:18:20.599 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-03 00:18:20.599 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-03 00:18:20.599 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-03 00:18:20.684 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-03 00:18:20.718 [39mDEBUG[0;39m 18864 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-03 00:18:20.915 [34mINFO [0;39m 18864 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-03 00:18:20.925 [34mINFO [0;39m 18864 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 00:18:20.926 [39mDEBUG[0;39m 18864 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 00:18:20.926 [39mDEBUG[0;39m 18864 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 00:18:20.926 [34mINFO [0;39m 18864 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-03 00:18:20.926 [39mDEBUG[0;39m 18864 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@685280dd]
2025-08-03 00:18:20.926 [39mDEBUG[0;39m 18864 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@685280dd]
2025-08-03 00:18:20.926 [34mINFO [0;39m 18864 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@685280dd]]
2025-08-03 00:18:20.926 [34mINFO [0;39m 18864 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-03 00:18:20.927 [39mDEBUG[0;39m 18864 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:18:20.927 [39mDEBUG[0;39m 18864 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 00:18:20.939 [34mINFO [0;39m 18864 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.503 seconds (JVM running for 6.013)
2025-08-03 00:18:29.050 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 00:18:29.051 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-08-03 00:18:29.051 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-08-03 00:18:29.051 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-08-03 00:18:29.051 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-08-03 00:18:29.053 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@332aa808
2025-08-03 00:18:29.053 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@40eddca9
2025-08-03 00:18:29.053 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-03 00:18:29.053 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed initialization in 2 ms
2025-08-03 00:18:29.066 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 00:18:29.069 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 00:18:29.076 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:18:29.534 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 00:18:29.585 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 00:18:29.591 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:18:29.594 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f41437e]
2025-08-03 00:18:29.600 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1817646823 wrapping com.mysql.cj.jdbc.ConnectionImpl@54f5d3fe] will be managed by Spring
2025-08-03 00:18:29.602 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 00:18:29.622 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 00:18:29.643 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 00:18:29.645 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f41437e]
2025-08-03 00:18:29.646 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 00:18:29.646 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f41437e]
2025-08-03 00:18:29.646 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f41437e]
2025-08-03 00:18:29.646 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f41437e]
2025-08-03 00:18:29.651 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 00:18:29.658 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 00:18:29.658 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 00:18:29.660 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 00:18:29.661 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 00:18:29.680 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I], priority=1 (truncated)...]
2025-08-03 00:18:29.772 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 1
2025-08-03 00:18:29.774 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 00:18:29.774 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc4ac21]
2025-08-03 00:18:29.774 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@773896332 wrapping com.mysql.cj.jdbc.ConnectionImpl@54f5d3fe] will be managed by Spring
2025-08-03 00:18:29.774 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 00:18:29.775 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 00:18:29.779 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 00:18:29.780 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc4ac21]
2025-08-03 00:18:29.780 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 00:18:29.781 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 00:18:29.781 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 00:18:29.781 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:18:29.781 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 00:18:29.781 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 00:18:29.812 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:18:29.839 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.w.r.f.client.ExchangeFunctions : [16d44258] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 00:18:30.478 [39mDEBUG[0;39m 18864 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [16d44258] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 00:18:31.132 [39mDEBUG[0;39m 18864 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [16d44258] [8bd5f3a8-1] Response 200 OK
2025-08-03 00:18:31.146 [39mDEBUG[0;39m 18864 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [16d44258] [8bd5f3a8-1] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you tod (truncated)..."
2025-08-03 00:18:31.147 [34mINFO [0;39m 18864 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 00:18:31.147 [39mDEBUG[0;39m 18864 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754151511,"id":"VzqOaLazBLmG6dkPkbWl2Qk","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":36}}
2025-08-03 00:18:31.147 [39mDEBUG[0;39m 18864 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754151511,"id":"VzqOaLazBLmG6dkPkbWl2Qk","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":36}}, created=null, model=null, choices=null, usage=null)
2025-08-03 00:18:31.150 [31mWARN [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应无效
2025-08-03 00:18:31.151 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 0, 失败: 1
2025-08-03 00:18:31.151 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc4ac21]
2025-08-03 00:18:31.151 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc4ac21]
2025-08-03 00:18:31.151 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc4ac21]
2025-08-03 00:18:31.153 [34mINFO [0;39m 18864 --- [http-nio-8080-exec-3] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 0, 失败: 1
2025-08-03 00:18:31.166 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 00:18:31.171 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[], failedKeys= (truncated)...]
2025-08-03 00:18:31.180 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 00:18:31.181 [39mDEBUG[0;39m 18864 --- [http-nio-8080-exec-3] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 00:19:20.578 [34mINFO [0;39m 18864 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-03 00:49:20.587 [34mINFO [0;39m 18864 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-08-03 01:05:37.345 [39mDEBUG[0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 01:05:37.346 [39mDEBUG[0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 01:05:37.346 [34mINFO [0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-03 01:05:37.346 [34mINFO [0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@685280dd]]
2025-08-03 01:05:37.346 [39mDEBUG[0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@685280dd]
2025-08-03 01:05:37.346 [39mDEBUG[0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@685280dd]
2025-08-03 01:05:37.346 [34mINFO [0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-03 01:05:37.346 [39mDEBUG[0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:05:37.346 [39mDEBUG[0;39m 18864 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:05:39.610 [34mINFO [0;39m 18864 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-03 01:05:39.614 [34mINFO [0;39m 18864 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-03 01:07:04.637 [34mINFO [0;39m 12020 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-03 01:07:04.646 [34mINFO [0;39m 12020 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 12020 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-03 01:07:04.646 [39mDEBUG[0;39m 12020 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-03 01:07:04.647 [34mINFO [0;39m 12020 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-03 01:07:05.624 [34mINFO [0;39m 12020 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03 01:07:05.627 [34mINFO [0;39m 12020 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-03 01:07:05.671 [34mINFO [0;39m 12020 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OpenAiRequestLogMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-03 01:07:05.766 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-03 01:07:05.768 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-03 01:07:05.769 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-03 01:07:05.769 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-03 01:07:05.770 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-03 01:07:05.770 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-03 01:07:05.770 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-03 01:07:05.770 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-03 01:07:05.770 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-03 01:07:05.770 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'openAiRequestLogMapper' and 'com.example.pure.mapper.primary.OpenAiRequestLogMapper' mapperInterface
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'openAiRequestLogMapper'.
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-03 01:07:05.771 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-03 01:07:05.772 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-03 01:07:05.772 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-03 01:07:05.772 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-03 01:07:05.772 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-03 01:07:05.772 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-03 01:07:05.772 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-03 01:07:05.773 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-03 01:07:05.773 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-03 01:07:05.773 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-03 01:07:05.773 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-03 01:07:05.773 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-03 01:07:05.773 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-03 01:07:05.774 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-03 01:07:05.774 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-03 01:07:05.774 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-03 01:07:05.774 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-03 01:07:05.774 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-03 01:07:05.774 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-03 01:07:05.775 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-03 01:07:05.775 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-03 01:07:05.775 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-03 01:07:05.775 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-03 01:07:05.776 [39mDEBUG[0;39m 12020 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-03 01:07:06.343 [34mINFO [0;39m 12020 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-03 01:07:06.349 [34mINFO [0;39m 12020 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-03 01:07:06.350 [34mINFO [0;39m 12020 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-03 01:07:06.350 [34mINFO [0;39m 12020 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-03 01:07:06.452 [34mINFO [0;39m 12020 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-03 01:07:06.453 [34mINFO [0;39m 12020 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1769 ms
2025-08-03 01:07:06.812 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-03 01:07:06.825 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-03 01:07:06.835 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-03 01:07:06.843 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-03 01:07:06.852 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-03 01:07:06.857 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-03 01:07:06.861 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-03 01:07:06.870 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-03 01:07:06.874 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-03 01:07:06.878 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-03 01:07:06.882 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-03 01:07:06.893 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-03 01:07:06.899 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-03 01:07:06.906 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-03 01:07:06.912 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-03 01:07:06.921 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-03 01:07:06.924 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-03 01:07:06.929 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-03 01:07:06.934 [39mDEBUG[0;39m 12020 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-03 01:07:06.949 [34mINFO [0;39m 12020 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-03 01:07:07.217 [34mINFO [0;39m 12020 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-03 01:07:07.711 [39mDEBUG[0;39m 12020 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-03 01:07:07.712 [39mDEBUG[0;39m 12020 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-03 01:07:08.007 [39mDEBUG[0;39m 12020 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 01:07:08.008 [39mDEBUG[0;39m 12020 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:07:08.457 [34mINFO [0;39m 12020 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-03 01:07:08.652 [34mINFO [0;39m 12020 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-03 01:07:08.661 [34mINFO [0;39m 12020 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-03 01:07:08.719 [34mINFO [0;39m 12020 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:07:08.719 [34mINFO [0;39m 12020 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:07:08.730 [34mINFO [0;39m 12020 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-03 01:07:08.733 [31mWARN [0;39m 12020 --- [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'aiConfigController' defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\controller\openai\AiConfigController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'aiConfigServiceImpl' defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\service\openai\impl\AiConfigServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 5; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'openAiCompatibleServiceImpl' defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\service\openai\impl\OpenAiCompatibleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: Error creating bean with name 'aiConfigServiceImpl': Requested bean is currently in creation: Is there an unresolvable circular reference?
2025-08-03 01:07:08.745 [34mINFO [0;39m 12020 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-03 01:07:08.752 [34mINFO [0;39m 12020 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-03 01:07:08.754 [34mINFO [0;39m 12020 --- [main] o.a.catalina.core.StandardService : Stopping service [Tomcat]
2025-08-03 01:07:08.755 [31mWARN [0;39m 12020 --- [main] o.a.c.loader.WebappClassLoaderBase : The web application [ROOT] appears to have started a thread named [Thread-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base@17.0.13/sun.net.dns.ResolverConfigurationImpl.notifyAddrChange0(Native Method)
 java.base@17.0.13/sun.net.dns.ResolverConfigurationImpl$AddressChangeListener.run(ResolverConfigurationImpl.java:176)
2025-08-03 01:07:08.765 [34mINFO [0;39m 12020 --- [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-03 01:07:08.780 [1;31mERROR[0;39m 12020 --- [main] o.s.b.d.LoggingFailureAnalysisReporter : 

***************************
APPLICATION FAILED TO START
***************************

Description:

The dependencies of some of the beans in the application context form a cycle:

   aiConfigController defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\controller\openai\AiConfigController.class]
┌─────┐
|  aiConfigServiceImpl defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\service\openai\impl\AiConfigServiceImpl.class]
↑     ↓
|  openAiCompatibleServiceImpl defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\service\openai\impl\OpenAiCompatibleServiceImpl.class]
└─────┘


Action:

Relying upon circular references is discouraged and they are prohibited by default. Update your application to remove the dependency cycle between beans. As a last resort, it may be possible to break the cycle automatically by setting spring.main.allow-circular-references to true.

2025-08-03 01:29:53.038 [34mINFO [0;39m 13596 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-03 01:29:53.044 [34mINFO [0;39m 13596 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 13596 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-03 01:29:53.045 [39mDEBUG[0;39m 13596 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-03 01:29:53.045 [34mINFO [0;39m 13596 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-03 01:29:54.003 [34mINFO [0;39m 13596 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03 01:29:54.005 [34mINFO [0;39m 13596 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-03 01:29:54.048 [34mINFO [0;39m 13596 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OpenAiRequestLogMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-03 01:29:54.136 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-03 01:29:54.137 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-03 01:29:54.137 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-03 01:29:54.137 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-03 01:29:54.137 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-03 01:29:54.137 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-03 01:29:54.137 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-03 01:29:54.138 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-03 01:29:54.139 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-03 01:29:54.139 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-03 01:29:54.140 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-03 01:29:54.140 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-03 01:29:54.140 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-03 01:29:54.140 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-03 01:29:54.140 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-03 01:29:54.140 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-03 01:29:54.141 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-03 01:29:54.141 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-03 01:29:54.141 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-03 01:29:54.141 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'openAiRequestLogMapper' and 'com.example.pure.mapper.primary.OpenAiRequestLogMapper' mapperInterface
2025-08-03 01:29:54.141 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'openAiRequestLogMapper'.
2025-08-03 01:29:54.141 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-03 01:29:54.142 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-03 01:29:54.143 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-03 01:29:54.143 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-03 01:29:54.143 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-03 01:29:54.143 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-03 01:29:54.143 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-03 01:29:54.143 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-03 01:29:54.144 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-03 01:29:54.144 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-03 01:29:54.144 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-03 01:29:54.144 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-03 01:29:54.145 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-03 01:29:54.145 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-03 01:29:54.145 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-03 01:29:54.145 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-03 01:29:54.146 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-03 01:29:54.146 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-03 01:29:54.146 [39mDEBUG[0;39m 13596 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-03 01:29:54.728 [34mINFO [0;39m 13596 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-03 01:29:54.733 [34mINFO [0;39m 13596 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-03 01:29:54.734 [34mINFO [0;39m 13596 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-03 01:29:54.734 [34mINFO [0;39m 13596 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-03 01:29:54.861 [34mINFO [0;39m 13596 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-03 01:29:54.861 [34mINFO [0;39m 13596 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1768 ms
2025-08-03 01:29:55.194 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-03 01:29:55.208 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-03 01:29:55.219 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-03 01:29:55.226 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-03 01:29:55.235 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-03 01:29:55.239 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-03 01:29:55.246 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-03 01:29:55.255 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-03 01:29:55.259 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-03 01:29:55.264 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-03 01:29:55.268 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-03 01:29:55.274 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-03 01:29:55.310 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-03 01:29:55.317 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-03 01:29:55.323 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-03 01:29:55.331 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-03 01:29:55.335 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-03 01:29:55.340 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-03 01:29:55.344 [39mDEBUG[0;39m 13596 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-03 01:29:55.358 [34mINFO [0;39m 13596 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-03 01:29:55.660 [34mINFO [0;39m 13596 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-03 01:29:56.166 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-03 01:29:56.166 [39mDEBUG[0;39m 13596 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-03 01:29:56.432 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 01:29:56.434 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:56.872 [34mINFO [0;39m 13596 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-03 01:29:57.003 [34mINFO [0;39m 13596 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-03 01:29:57.011 [34mINFO [0;39m 13596 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-03 01:29:57.072 [34mINFO [0;39m 13596 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:29:57.072 [34mINFO [0;39m 13596 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:29:57.084 [34mINFO [0;39m 13596 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-03 01:29:57.121 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:29:57.121 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.122 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-03 01:29:57.122 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.122 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-03 01:29:57.123 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.123 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 01:29:57.123 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.123 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-03 01:29:57.124 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.124 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:29:57.124 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.128 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-03 01:29:57.128 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.128 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:29:57.128 [39mDEBUG[0;39m 13596 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:29:57.261 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-03 01:29:57.263 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-03 01:29:57.269 [34mINFO [0;39m 13596 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@35c69e6b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@78e386a5, org.springframework.security.web.context.SecurityContextPersistenceFilter@77a14911, org.springframework.security.web.header.HeaderWriterFilter@72bef795, org.springframework.security.web.authentication.logout.LogoutFilter@5d7f4cbb, com.example.pure.filter.JwtFilter@226e95e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1142843c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3993cecb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6138ee0e, org.springframework.security.web.session.SessionManagementFilter@3b2317b7, org.springframework.security.web.access.ExceptionTranslationFilter@77d0a492, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ceeb592]
2025-08-03 01:29:57.271 [34mINFO [0;39m 13596 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-03 01:29:57.280 [34mINFO [0;39m 13596 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-03 01:29:57.281 [34mINFO [0;39m 13596 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-03 01:29:57.282 [34mINFO [0;39m 13596 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-03 01:29:57.480 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-03 01:29:57.496 [34mINFO [0;39m 13596 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-03 01:29:57.555 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 112 mappings in 'requestMappingHandlerMapping'
2025-08-03 01:29:57.562 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-03 01:29:58.001 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-03 01:29:58.149 [34mINFO [0;39m 13596 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-03 01:29:58.167 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-03 01:29:58.168 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-03 01:29:58.169 [39mDEBUG[0;39m 13596 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-03 01:29:58.170 [34mINFO [0;39m 13596 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@58a06477, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1c4335c8, org.springframework.security.web.context.SecurityContextPersistenceFilter@74727e41, org.springframework.security.web.header.HeaderWriterFilter@51d50b5f, org.springframework.web.filter.CorsFilter@5e2abb58, org.springframework.security.web.authentication.logout.LogoutFilter@2c914364, com.example.pure.filter.JwtFilter@226e95e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@16117d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@78977b59, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@943d0c2, org.springframework.security.web.session.SessionManagementFilter@38816a6c, org.springframework.security.web.access.ExceptionTranslationFilter@242a3997, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@258e5601]
2025-08-03 01:29:58.201 [39mTRACE[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@43ed0ff3, started on Sun Aug 03 01:29:53 CST 2025
2025-08-03 01:29:58.215 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-03 01:29:58.215 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-03 01:29:58.216 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-03 01:29:58.217 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiRequestLogController:
	
2025-08-03 01:29:58.217 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-03 01:29:58.220 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/echo]}: echo(String)
2025-08-03 01:29:58.221 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-03 01:29:58.221 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-03 01:29:58.221 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-03 01:29:58.221 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-03 01:29:58.221 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-03 01:29:58.222 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-03 01:29:58.222 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-03 01:29:58.222 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-03 01:29:58.316 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-03 01:29:58.350 [39mDEBUG[0;39m 13596 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-03 01:29:58.555 [34mINFO [0;39m 13596 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-03 01:29:58.565 [34mINFO [0;39m 13596 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 01:29:58.566 [39mDEBUG[0;39m 13596 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 01:29:58.566 [39mDEBUG[0;39m 13596 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 01:29:58.566 [34mINFO [0;39m 13596 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-03 01:29:58.566 [39mDEBUG[0;39m 13596 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3846ba32]
2025-08-03 01:29:58.566 [39mDEBUG[0;39m 13596 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3846ba32]
2025-08-03 01:29:58.566 [34mINFO [0;39m 13596 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3846ba32]]
2025-08-03 01:29:58.567 [34mINFO [0;39m 13596 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-03 01:29:58.567 [39mDEBUG[0;39m 13596 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:29:58.567 [39mDEBUG[0;39m 13596 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:29:58.578 [34mINFO [0;39m 13596 --- [main] com.example.pure.PureApplication : Started PureApplication in 5.948 seconds (JVM running for 6.456)
2025-08-03 01:30:58.195 [34mINFO [0;39m 13596 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-03 01:31:11.090 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 01:31:11.090 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-08-03 01:31:11.090 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-08-03 01:31:11.090 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-08-03 01:31:11.090 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-08-03 01:31:11.093 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@63d7f842
2025-08-03 01:31:11.094 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@7e43095e
2025-08-03 01:31:11.094 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-03 01:31:11.094 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-08-03 01:31:11.107 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 01:31:11.109 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 01:31:11.116 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:31:11.625 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 01:31:11.644 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:31:11.648 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f8f1ea]
2025-08-03 01:31:11.654 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1808662555 wrapping com.mysql.cj.jdbc.ConnectionImpl@42e34c42] will be managed by Spring
2025-08-03 01:31:11.656 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==>  Preparing: SELECT * FROM user WHERE username =?
2025-08-03 01:31:11.682 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : ==> Parameters: admin(String)
2025-08-03 01:31:11.709 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.U.findByUserWithPasswordByUsername : <==      Total: 1
2025-08-03 01:31:11.711 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f8f1ea]
2025-08-03 01:31:11.712 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 01:31:11.712 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f8f1ea] from current transaction
2025-08-03 01:31:11.712 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 01:31:11.713 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 01:31:11.715 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 01:31:11.715 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f8f1ea]
2025-08-03 01:31:11.717 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 01:31:11.718 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f8f1ea]
2025-08-03 01:31:11.718 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f8f1ea]
2025-08-03 01:31:11.718 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51f8f1ea]
2025-08-03 01:31:11.764 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 01:31:11.771 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 01:31:11.771 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 01:31:11.773 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 01:31:11.775 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:31:11.810 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I], priority=1 (truncated)...]
2025-08-03 01:31:11.941 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 1
2025-08-03 01:31:11.942 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:31:11.942 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d1584e7]
2025-08-03 01:31:11.942 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@42e34c42] will be managed by Spring
2025-08-03 01:31:11.942 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 01:31:11.942 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 01:31:11.948 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 01:31:11.949 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d1584e7]
2025-08-03 01:31:11.949 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:31:11.950 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:31:11.951 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:31:11.951 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:31:11.951 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 01:31:11.951 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:31:11.981 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:31:12.003 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [50ef7130] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:31:12.422 [39mDEBUG[0;39m 13596 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [50ef7130] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:31:21.993 [39mDEBUG[0;39m 13596 --- [parallel-1] o.s.w.r.f.client.ExchangeFunctions : [50ef7130] Cancel signal (to close connection)
2025-08-03 01:31:22.007 [31mWARN [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥实际请求测试失败 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 错误: java.util.concurrent.TimeoutException: Did not observe any item or terminal signal within 10000ms in 'peekTerminal' (and no fallback has been configured)
2025-08-03 01:31:22.007 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 0, 失败: 1
2025-08-03 01:31:22.007 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d1584e7]
2025-08-03 01:31:22.007 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d1584e7]
2025-08-03 01:31:22.007 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d1584e7]
2025-08-03 01:31:22.009 [34mINFO [0;39m 13596 --- [http-nio-8080-exec-2] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 0, 失败: 1
2025-08-03 01:31:22.016 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 01:31:22.022 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[], failedKeys= (truncated)...]
2025-08-03 01:31:22.032 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 01:31:22.032 [39mDEBUG[0;39m 13596 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 01:32:35.808 [39mDEBUG[0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 01:32:35.808 [39mDEBUG[0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 01:32:35.808 [34mINFO [0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-03 01:32:35.808 [34mINFO [0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3846ba32]]
2025-08-03 01:32:35.808 [39mDEBUG[0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3846ba32]
2025-08-03 01:32:35.808 [39mDEBUG[0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3846ba32]
2025-08-03 01:32:35.808 [34mINFO [0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-03 01:32:35.808 [39mDEBUG[0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:32:35.808 [39mDEBUG[0;39m 13596 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:32:38.028 [34mINFO [0;39m 13596 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-03 01:32:38.035 [34mINFO [0;39m 13596 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-03 01:32:42.327 [34mINFO [0;39m 16552 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-03 01:32:42.333 [34mINFO [0;39m 16552 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 16552 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-03 01:32:42.334 [39mDEBUG[0;39m 16552 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-03 01:32:42.334 [34mINFO [0;39m 16552 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-03 01:32:43.358 [34mINFO [0;39m 16552 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03 01:32:43.360 [34mINFO [0;39m 16552 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-03 01:32:43.402 [34mINFO [0;39m 16552 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OpenAiRequestLogMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-03 01:32:43.501 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-03 01:32:43.502 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-03 01:32:43.502 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-03 01:32:43.502 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-03 01:32:43.502 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-03 01:32:43.502 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-03 01:32:43.503 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-03 01:32:43.504 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-03 01:32:43.504 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-03 01:32:43.504 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-03 01:32:43.504 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-03 01:32:43.504 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'openAiRequestLogMapper' and 'com.example.pure.mapper.primary.OpenAiRequestLogMapper' mapperInterface
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'openAiRequestLogMapper'.
2025-08-03 01:32:43.505 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-03 01:32:43.506 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-03 01:32:43.506 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-03 01:32:43.506 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-03 01:32:43.506 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-03 01:32:43.506 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-03 01:32:43.506 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-03 01:32:43.507 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-03 01:32:43.507 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-03 01:32:43.507 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-03 01:32:43.507 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-03 01:32:43.507 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-03 01:32:43.507 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-03 01:32:43.508 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-03 01:32:43.508 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-03 01:32:43.508 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-03 01:32:43.508 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-03 01:32:43.508 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-03 01:32:43.509 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-03 01:32:43.509 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-03 01:32:43.509 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-03 01:32:43.509 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-03 01:32:43.509 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-03 01:32:43.510 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-03 01:32:43.510 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-03 01:32:43.510 [39mDEBUG[0;39m 16552 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-03 01:32:44.218 [34mINFO [0;39m 16552 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-03 01:32:44.225 [34mINFO [0;39m 16552 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-03 01:32:44.226 [34mINFO [0;39m 16552 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-03 01:32:44.226 [34mINFO [0;39m 16552 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-03 01:32:44.324 [34mINFO [0;39m 16552 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-03 01:32:44.324 [34mINFO [0;39m 16552 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1944 ms
2025-08-03 01:32:44.651 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-03 01:32:44.662 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-03 01:32:44.672 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-03 01:32:44.685 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-03 01:32:44.693 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-03 01:32:44.697 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-03 01:32:44.702 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-03 01:32:44.711 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-03 01:32:44.715 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-03 01:32:44.720 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-03 01:32:44.724 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-03 01:32:44.730 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-03 01:32:44.737 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-03 01:32:44.749 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-03 01:32:44.758 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-03 01:32:44.772 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-03 01:32:44.779 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-03 01:32:44.786 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-03 01:32:44.792 [39mDEBUG[0;39m 16552 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-03 01:32:44.814 [34mINFO [0;39m 16552 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-03 01:32:45.176 [34mINFO [0;39m 16552 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-03 01:32:45.820 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-03 01:32:45.820 [39mDEBUG[0;39m 16552 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-03 01:32:46.154 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 01:32:46.155 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.668 [34mINFO [0;39m 16552 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-03 01:32:46.815 [34mINFO [0;39m 16552 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-03 01:32:46.824 [34mINFO [0;39m 16552 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-03 01:32:46.895 [34mINFO [0;39m 16552 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:32:46.895 [34mINFO [0;39m 16552 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:32:46.908 [34mINFO [0;39m 16552 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-03 01:32:46.939 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:32:46.939 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.939 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-03 01:32:46.940 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.940 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-03 01:32:46.940 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.941 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:32:46.941 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.941 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 01:32:46.941 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.941 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-03 01:32:46.941 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.946 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:32:46.946 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:46.946 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-03 01:32:46.947 [39mDEBUG[0;39m 16552 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-03 01:32:47.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-03 01:32:47.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-03 01:32:47.089 [34mINFO [0;39m 16552 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@59e24c62, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5379488c, org.springframework.security.web.context.SecurityContextPersistenceFilter@55e75ce0, org.springframework.security.web.header.HeaderWriterFilter@a23b96b, org.springframework.security.web.authentication.logout.LogoutFilter@734eada1, com.example.pure.filter.JwtFilter@60e5eed0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35e63d26, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5d51203b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@744c16fb, org.springframework.security.web.session.SessionManagementFilter@5a919251, org.springframework.security.web.access.ExceptionTranslationFilter@15a87fbb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2620264e]
2025-08-03 01:32:47.091 [34mINFO [0;39m 16552 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-03 01:32:47.093 [34mINFO [0;39m 16552 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-03 01:32:47.094 [34mINFO [0;39m 16552 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-03 01:32:47.094 [34mINFO [0;39m 16552 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-03 01:32:47.289 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-03 01:32:47.308 [34mINFO [0;39m 16552 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-03 01:32:47.369 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 112 mappings in 'requestMappingHandlerMapping'
2025-08-03 01:32:47.376 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-03 01:32:47.812 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-03 01:32:48.048 [34mINFO [0;39m 16552 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-03 01:32:48.084 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-03 01:32:48.084 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-03 01:32:48.084 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-03 01:32:48.084 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-03 01:32:48.084 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-03 01:32:48.084 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-03 01:32:48.084 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-03 01:32:48.085 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-03 01:32:48.086 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-03 01:32:48.087 [39mDEBUG[0;39m 16552 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-03 01:32:48.088 [34mINFO [0;39m 16552 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@650929dc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1435a8ca, org.springframework.security.web.context.SecurityContextPersistenceFilter@4a66949a, org.springframework.security.web.header.HeaderWriterFilter@12d24c43, org.springframework.web.filter.CorsFilter@781befe7, org.springframework.security.web.authentication.logout.LogoutFilter@514eefc4, com.example.pure.filter.JwtFilter@60e5eed0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@57e83608, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@57d8d8e2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@69780093, org.springframework.security.web.session.SessionManagementFilter@6bf27411, org.springframework.security.web.access.ExceptionTranslationFilter@1dd01876, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@18213a65]
2025-08-03 01:32:48.153 [39mTRACE[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4cc61eb1, started on Sun Aug 03 01:32:42 CST 2025
2025-08-03 01:32:48.180 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-03 01:32:48.181 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-03 01:32:48.182 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-03 01:32:48.183 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-03 01:32:48.183 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-03 01:32:48.184 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-03 01:32:48.184 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-03 01:32:48.184 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-03 01:32:48.184 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiRequestLogController:
	
2025-08-03 01:32:48.184 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-03 01:32:48.200 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-08-03 01:32:48.202 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-03 01:32:48.202 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-03 01:32:48.204 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-03 01:32:48.205 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-03 01:32:48.210 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-03 01:32:48.213 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-03 01:32:48.213 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-03 01:32:48.214 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-03 01:32:48.328 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-03 01:32:48.373 [39mDEBUG[0;39m 16552 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-03 01:32:48.618 [34mINFO [0;39m 16552 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-03 01:32:48.632 [34mINFO [0;39m 16552 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 01:32:48.633 [39mDEBUG[0;39m 16552 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 01:32:48.634 [39mDEBUG[0;39m 16552 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 01:32:48.634 [34mINFO [0;39m 16552 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-03 01:32:48.634 [39mDEBUG[0;39m 16552 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@27449f65]
2025-08-03 01:32:48.634 [39mDEBUG[0;39m 16552 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@27449f65]
2025-08-03 01:32:48.634 [34mINFO [0;39m 16552 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@27449f65]]
2025-08-03 01:32:48.634 [34mINFO [0;39m 16552 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-03 01:32:48.634 [39mDEBUG[0;39m 16552 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:32:48.634 [39mDEBUG[0;39m 16552 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:32:48.648 [34mINFO [0;39m 16552 --- [main] com.example.pure.PureApplication : Started PureApplication in 6.758 seconds (JVM running for 7.979)
2025-08-03 01:32:53.077 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 01:32:53.077 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-08-03 01:32:53.077 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-08-03 01:32:53.077 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-08-03 01:32:53.077 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-08-03 01:32:53.079 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1dfcf038
2025-08-03 01:32:53.079 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@4969fc94
2025-08-03 01:32:53.080 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-03 01:32:53.080 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 3 ms
2025-08-03 01:32:53.096 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 01:32:53.099 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 01:32:53.109 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:32:53.626 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 01:32:53.678 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 01:32:53.685 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:32:53.689 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33dad882]
2025-08-03 01:32:53.696 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1104675932 wrapping com.mysql.cj.jdbc.ConnectionImpl@1cf1c210] will be managed by Spring
2025-08-03 01:32:53.699 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 01:32:53.733 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 01:32:53.772 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 01:32:53.775 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33dad882]
2025-08-03 01:32:53.777 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 01:32:53.778 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33dad882]
2025-08-03 01:32:53.778 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33dad882]
2025-08-03 01:32:53.778 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33dad882]
2025-08-03 01:32:53.784 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 01:32:53.793 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 01:32:53.794 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 01:32:53.796 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 01:32:53.798 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:32:53.824 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I], priority=1 (truncated)...]
2025-08-03 01:32:53.975 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 1
2025-08-03 01:32:53.977 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:32:53.977 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:32:53.977 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@1cf1c210] will be managed by Spring
2025-08-03 01:32:53.978 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 01:32:53.978 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 01:32:53.982 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 01:32:53.982 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:32:53.983 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:32:53.983 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:32:53.984 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:32:53.984 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:32:53.984 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 01:32:53.984 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:32:54.022 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:32:54.052 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [7ef4505c] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:32:54.487 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [7ef4505c] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:32:55.141 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [7ef4505c] [acc9923d-1] Response 200 OK
2025-08-03 01:32:55.157 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [7ef4505c] [acc9923d-1] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you tod (truncated)..."
2025-08-03 01:32:55.158 [34mINFO [0;39m 16552 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 01:32:55.158 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754155975,"id":"x0uOaJe6Bo-kmtkP3eSlgA8","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":47}}
2025-08-03 01:32:55.158 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754155975,"id":"x0uOaJe6Bo-kmtkP3eSlgA8","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":47}}, created=null, model=null, choices=null, usage=null)
2025-08-03 01:32:55.186 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: Hi there! How can I help you today?
2025-08-03 01:34:33.622 [34mINFO [0;39m 16552 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-03 01:34:33.622 [31mWARN [0;39m 16552 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m48s343ms131µs400ns).
2025-08-03 01:34:47.089 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试成功 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应长度: 99
2025-08-03 01:35:27.442 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8] from current transaction
2025-08-03 01:35:27.443 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-03 01:35:27.448 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 3(Long), 84d2a7d854a125724531dbf8bd4a031cce006ba0ba67776105c531cf351963dfe1a846e4c7621cf834317719d08c3c301cfc5bcb786be7976abd0f0e307447f0(String), true(Boolean), 1(Integer)
2025-08-03 01:35:27.466 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-03 01:35:27.472 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:35:29.478 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 1
2025-08-03 01:35:29.478 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8] from current transaction
2025-08-03 01:35:29.479 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:35:29.483 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 1(Long)
2025-08-03 01:35:29.487 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-03 01:35:29.487 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:35:29.489 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8] from current transaction
2025-08-03 01:35:29.490 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, is_healthy, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-03 01:35:29.496 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==> Parameters: 1(Long), 3(Long), 1(Long), 0(Integer), 0(Long), 0(Integer), true(Boolean), 2025-08-03 01:35:29.4895336(Timestamp)
2025-08-03 01:35:29.507 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : <==    Updates: 1
2025-08-03 01:35:29.508 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:35:29.508 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : API密钥负载状态初始化完成 - ID: 1
2025-08-03 01:35:36.413 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 3, 密钥: AIza****Pr7I
2025-08-03 01:35:53.555 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 1, 失败: 0
2025-08-03 01:36:03.615 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:36:03.615 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:36:03.615 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ac7dad8]
2025-08-03 01:36:03.622 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-2] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 1, 失败: 0
2025-08-03 01:36:03.635 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 01:36:03.638 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[ApiKeyDto(id=1 (truncated)...]
2025-08-03 01:36:03.647 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 01:36:03.648 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 01:37:59.182 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 01:37:59.182 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 01:37:59.183 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:37:59.262 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 01:37:59.265 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 01:37:59.265 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:37:59.265 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c97d387]
2025-08-03 01:37:59.265 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1964256372 wrapping com.mysql.cj.jdbc.ConnectionImpl@1cf1c210] will be managed by Spring
2025-08-03 01:37:59.265 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 01:37:59.265 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 01:37:59.267 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 01:37:59.267 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c97d387]
2025-08-03 01:37:59.267 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 01:37:59.267 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c97d387]
2025-08-03 01:37:59.267 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c97d387]
2025-08-03 01:37:59.267 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c97d387]
2025-08-03 01:37:59.269 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 01:37:59.270 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 01:37:59.270 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 01:37:59.270 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 01:37:59.271 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:37:59.271 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I], priority=1 (truncated)...]
2025-08-03 01:37:59.273 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 1
2025-08-03 01:37:59.274 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:37:59.274 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:37:59.274 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@564395637 wrapping com.mysql.cj.jdbc.ConnectionImpl@1cf1c210] will be managed by Spring
2025-08-03 01:37:59.274 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 01:37:59.274 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 01:37:59.276 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 01:37:59.276 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:37:59.276 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:37:59.276 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:37:59.276 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:37:59.276 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:37:59.276 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 01:37:59.276 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:37:59.277 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:37:59.278 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.w.r.f.client.ExchangeFunctions : [3d1fe181] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:37:59.479 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [3d1fe181] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:38:00.202 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [3d1fe181] [d8e3dc2a-1] Response 200 OK
2025-08-03 01:38:00.204 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [3d1fe181] [d8e3dc2a-1] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you tod (truncated)..."
2025-08-03 01:38:00.204 [34mINFO [0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 01:38:00.204 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156280,"id":"-EyOaLT-CrmG6dkPkbWl2Qk","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":44}}
2025-08-03 01:38:00.204 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156280,"id":"-EyOaLT-CrmG6dkPkbWl2Qk","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":44}}, created=null, model=null, choices=null, usage=null)
2025-08-03 01:38:00.205 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: Hi there! How can I help you today?
2025-08-03 01:38:00.205 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试成功 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应长度: 99
2025-08-03 01:38:00.205 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c] from current transaction
2025-08-03 01:38:00.205 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-03 01:38:00.205 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 3(Long), ae88a9f3c9e82b0eba09be6f638c9a64154ebe52c51c8b4016a29110be4e13e3fb4d3a5f0f72d52dda2c6fc42c5abfe8359bdb9bdf5b36d991e040e5999c25e1(String), true(Boolean), 1(Integer)
2025-08-03 01:38:00.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-03 01:38:00.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:38:00.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 2
2025-08-03 01:38:00.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c] from current transaction
2025-08-03 01:38:00.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:38:00.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 2(Long)
2025-08-03 01:38:00.209 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-03 01:38:00.209 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:38:00.210 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c] from current transaction
2025-08-03 01:38:00.210 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, is_healthy, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-03 01:38:00.210 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.A.insert : ==> Parameters: 1(Long), 3(Long), 2(Long), 0(Integer), 0(Long), 0(Integer), true(Boolean), 2025-08-03 01:38:00.2109827(Timestamp)
2025-08-03 01:38:00.212 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.m.p.A.insert : <==    Updates: 1
2025-08-03 01:38:00.212 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:38:00.213 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.i.LoadBalancerServiceImpl : API密钥负载状态初始化完成 - ID: 2
2025-08-03 01:38:00.213 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 3, 密钥: AIza****Pr7I
2025-08-03 01:38:00.213 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 1, 失败: 0
2025-08-03 01:38:00.213 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:38:00.213 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:38:00.213 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6a77fe8c]
2025-08-03 01:38:00.232 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-4] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 1, 成功: 1, 失败: 0
2025-08-03 01:38:00.233 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 01:38:00.233 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[ApiKeyDto(id=2 (truncated)...]
2025-08-03 01:38:00.234 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 01:38:00.234 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-4] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 01:39:03.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 01:39:03.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 01:39:03.208 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:39:03.290 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 01:39:03.294 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 01:39:03.294 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:39:03.294 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51fdb4f5]
2025-08-03 01:39:03.294 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@1765845427 wrapping com.mysql.cj.jdbc.ConnectionImpl@1cf1c210] will be managed by Spring
2025-08-03 01:39:03.294 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 01:39:03.294 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 01:39:03.296 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 01:39:03.296 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51fdb4f5]
2025-08-03 01:39:03.296 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 01:39:03.296 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51fdb4f5]
2025-08-03 01:39:03.296 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51fdb4f5]
2025-08-03 01:39:03.296 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@51fdb4f5]
2025-08-03 01:39:03.298 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 01:39:03.299 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 01:39:03.299 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 01:39:03.299 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 01:39:03.300 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:39:03.301 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBADRs22zUsnIpbbOjB5MaZqLmaa9IixyI, AIzaSyDKiHx (truncated)...]
2025-08-03 01:39:03.303 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 2
2025-08-03 01:39:03.303 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:39:03.303 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:03.303 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@863172289 wrapping com.mysql.cj.jdbc.ConnectionImpl@1cf1c210] will be managed by Spring
2025-08-03 01:39:03.303 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 01:39:03.304 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 01:39:03.306 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 01:39:03.306 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:03.306 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:39:03.306 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:39:03.306 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:39:03.306 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:39:03.306 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBADR...
2025-08-03 01:39:03.306 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:39:03.308 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:39:03.308 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.w.r.f.client.ExchangeFunctions : [729378e4] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:39:03.309 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [729378e4] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:39:04.546 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [729378e4] [d8e3dc2a-2] Response 200 OK
2025-08-03 01:39:04.548 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [729378e4] [d8e3dc2a-2] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you tod (truncated)..."
2025-08-03 01:39:04.548 [34mINFO [0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 01:39:04.549 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156344,"id":"OE2OaIi5IJaIqtsPgI778Ao","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":161}}
2025-08-03 01:39:04.549 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156344,"id":"OE2OaIi5IJaIqtsPgI778Ao","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":161}}, created=null, model=null, choices=null, usage=null)
2025-08-03 01:39:04.549 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: Hi there! How can I help you today?
2025-08-03 01:39:04.549 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试成功 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应长度: 99
2025-08-03 01:39:04.549 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04] from current transaction
2025-08-03 01:39:04.549 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-03 01:39:04.550 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 3(Long), d5fad5f8d5046046df4001c34f7f176d9f45dbb41abe2e79f08da4950ee412be92ea5e72fc5167c2d0744e91085e6c7ba252fe0e56f3d6c14eed66e922330de8(String), true(Boolean), 1(Integer)
2025-08-03 01:39:04.553 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-03 01:39:04.553 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:04.553 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 3
2025-08-03 01:39:04.553 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04] from current transaction
2025-08-03 01:39:04.553 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:39:04.553 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 3(Long)
2025-08-03 01:39:04.554 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-03 01:39:04.555 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:04.555 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04] from current transaction
2025-08-03 01:39:04.555 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, is_healthy, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-03 01:39:04.556 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.insert : ==> Parameters: 1(Long), 3(Long), 3(Long), 0(Integer), 0(Long), 0(Integer), true(Boolean), 2025-08-03 01:39:04.555264(Timestamp)
2025-08-03 01:39:04.558 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.insert : <==    Updates: 1
2025-08-03 01:39:04.558 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:04.558 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.LoadBalancerServiceImpl : API密钥负载状态初始化完成 - ID: 3
2025-08-03 01:39:04.558 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 3, 密钥: AIza****ixyI
2025-08-03 01:39:04.558 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:39:04.558 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:39:04.558 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:39:04.558 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:39:04.559 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-08-03 01:39:04.559 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:39:04.559 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:39:04.561 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.w.r.f.client.ExchangeFunctions : [dcc6298] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:39:04.561 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [dcc6298] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:39:06.050 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions : [dcc6298] [d8e3dc2a-3] Response 200 OK
2025-08-03 01:39:06.051 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] org.springframework.web.HttpLogging : [dcc6298] [d8e3dc2a-3] Decoded "{"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":175415634 (truncated)..."
2025-08-03 01:39:06.051 [34mINFO [0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 01:39:06.051 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":1754156346,"id":"Ok2OaKGEAtuVmtkP9IiqiQs","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":0,"prompt_tokens":2,"total_tokens":201}}
2025-08-03 01:39:06.051 [39mDEBUG[0;39m 16552 --- [reactor-http-nio-3] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"length","index":0,"message":{"role":"assistant"}}],"created":1754156346,"id":"Ok2OaKGEAtuVmtkP9IiqiQs","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":0,"prompt_tokens":2,"total_tokens":201}}, created=null, model=null, choices=null, usage=null)
2025-08-03 01:39:06.052 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试成功 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应长度: 0
2025-08-03 01:39:06.052 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04] from current transaction
2025-08-03 01:39:06.052 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-03 01:39:06.053 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 3(Long), 3b1123799838bda6c305e5ec5ef95a808887a145c3c309389cdae08656799f2351c6fabf9cfa6ec7d72a99ff74f0b90492fa96c3521cdf80fc34c0c7483008c2(String), true(Boolean), 1(Integer)
2025-08-03 01:39:06.056 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-03 01:39:06.057 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:06.057 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 4
2025-08-03 01:39:06.057 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04] from current transaction
2025-08-03 01:39:06.057 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:39:06.057 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 4(Long)
2025-08-03 01:39:06.059 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-03 01:39:06.059 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:06.059 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04] from current transaction
2025-08-03 01:39:06.060 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, is_healthy, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-03 01:39:06.060 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.insert : ==> Parameters: 1(Long), 3(Long), 4(Long), 0(Integer), 0(Long), 0(Integer), true(Boolean), 2025-08-03 01:39:06.0595004(Timestamp)
2025-08-03 01:39:06.062 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.m.p.A.insert : <==    Updates: 1
2025-08-03 01:39:06.062 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:06.062 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.i.LoadBalancerServiceImpl : API密钥负载状态初始化完成 - ID: 4
2025-08-03 01:39:06.062 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 3, 密钥: AIza****wmUU
2025-08-03 01:39:06.062 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 2, 成功: 2, 失败: 0
2025-08-03 01:39:06.062 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:06.062 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:06.062 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7accce04]
2025-08-03 01:39:06.072 [34mINFO [0;39m 16552 --- [http-nio-8080-exec-7] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 2, 成功: 2, 失败: 0
2025-08-03 01:39:06.073 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 01:39:06.073 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[ApiKeyDto(id=3 (truncated)...]
2025-08-03 01:39:06.074 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 01:39:06.074 [39mDEBUG[0;39m 16552 --- [http-nio-8080-exec-7] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 01:48:00.753 [39mDEBUG[0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 01:48:00.753 [39mDEBUG[0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 01:48:00.753 [34mINFO [0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-03 01:48:00.753 [34mINFO [0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@27449f65]]
2025-08-03 01:48:00.753 [39mDEBUG[0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@27449f65]
2025-08-03 01:48:00.753 [39mDEBUG[0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@27449f65]
2025-08-03 01:48:00.753 [34mINFO [0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-03 01:48:00.753 [39mDEBUG[0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:48:00.753 [39mDEBUG[0;39m 16552 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:48:03.061 [34mINFO [0;39m 16552 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-03 01:48:03.204 [34mINFO [0;39m 16552 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-03 01:48:11.007 [34mINFO [0;39m 22612 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-03 01:48:11.017 [34mINFO [0;39m 22612 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 22612 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-03 01:48:11.018 [39mDEBUG[0;39m 22612 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-03 01:48:11.018 [34mINFO [0;39m 22612 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-03 01:48:12.305 [34mINFO [0;39m 22612 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03 01:48:12.307 [34mINFO [0;39m 22612 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-03 01:48:12.360 [34mINFO [0;39m 22612 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-08-03 01:48:12.481 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OpenAiRequestLogMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-03 01:48:12.482 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-03 01:48:12.484 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-03 01:48:12.485 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-03 01:48:12.485 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-03 01:48:12.486 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-03 01:48:12.486 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-03 01:48:12.486 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-03 01:48:12.486 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'openAiRequestLogMapper' and 'com.example.pure.mapper.primary.OpenAiRequestLogMapper' mapperInterface
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'openAiRequestLogMapper'.
2025-08-03 01:48:12.487 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-03 01:48:12.488 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-03 01:48:12.488 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-03 01:48:12.488 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-03 01:48:12.488 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-03 01:48:12.488 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-03 01:48:12.488 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-03 01:48:12.489 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-03 01:48:12.489 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-03 01:48:12.489 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-03 01:48:12.489 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-03 01:48:12.490 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-03 01:48:12.490 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-03 01:48:12.490 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-03 01:48:12.490 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-03 01:48:12.491 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-03 01:48:12.491 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-03 01:48:12.491 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-03 01:48:12.491 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-03 01:48:12.492 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-03 01:48:12.492 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-03 01:48:12.492 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-03 01:48:12.492 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-03 01:48:12.492 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-03 01:48:12.492 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-03 01:48:12.493 [39mDEBUG[0;39m 22612 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-03 01:48:13.234 [34mINFO [0;39m 22612 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-03 01:48:13.241 [34mINFO [0;39m 22612 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-03 01:48:13.242 [34mINFO [0;39m 22612 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-03 01:48:13.243 [34mINFO [0;39m 22612 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-03 01:48:13.362 [34mINFO [0;39m 22612 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-03 01:48:13.362 [34mINFO [0;39m 22612 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2284 ms
2025-08-03 01:48:13.747 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-03 01:48:13.769 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-03 01:48:13.790 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-03 01:48:14.041 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-03 01:48:14.145 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-03 01:48:14.197 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-03 01:48:14.241 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-03 01:48:14.292 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-03 01:48:14.308 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-03 01:48:14.318 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-03 01:48:14.326 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-03 01:48:14.335 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-03 01:48:14.345 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-03 01:48:14.365 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-03 01:48:14.381 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-03 01:48:14.412 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-03 01:48:14.420 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-03 01:48:14.427 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-03 01:48:14.446 [39mDEBUG[0;39m 22612 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-03 01:48:14.471 [34mINFO [0;39m 22612 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-03 01:48:14.927 [34mINFO [0;39m 22612 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-03 01:48:15.596 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-03 01:48:15.597 [39mDEBUG[0;39m 22612 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-03 01:48:16.003 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 01:48:16.005 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.584 [34mINFO [0;39m 22612 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-03 01:48:16.803 [34mINFO [0;39m 22612 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-03 01:48:16.812 [34mINFO [0;39m 22612 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-03 01:48:16.881 [34mINFO [0;39m 22612 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:48:16.881 [34mINFO [0;39m 22612 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-03 01:48:16.894 [34mINFO [0;39m 22612 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-03 01:48:16.928 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:48:16.928 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.929 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-03 01:48:16.930 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.930 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-03 01:48:16.930 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.930 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 01:48:16.931 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.931 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-03 01:48:16.931 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.931 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:48:16.931 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.937 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-03 01:48:16.937 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:16.938 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 01:48:16.938 [39mDEBUG[0;39m 22612 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 01:48:17.084 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-03 01:48:17.085 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-03 01:48:17.089 [34mINFO [0;39m 22612 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@7f9b46ed, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4d0b8b8b, org.springframework.security.web.context.SecurityContextPersistenceFilter@2da3d7d3, org.springframework.security.web.header.HeaderWriterFilter@59b8a801, org.springframework.security.web.authentication.logout.LogoutFilter@6d92bfaf, com.example.pure.filter.JwtFilter@2acbe46d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47184859, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@22781286, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f18445b, org.springframework.security.web.session.SessionManagementFilter@5f7dbdfa, org.springframework.security.web.access.ExceptionTranslationFilter@74f54f8e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6c09d716]
2025-08-03 01:48:17.091 [34mINFO [0;39m 22612 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-03 01:48:17.093 [34mINFO [0;39m 22612 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-03 01:48:17.094 [34mINFO [0;39m 22612 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-03 01:48:17.094 [34mINFO [0;39m 22612 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-03 01:48:17.277 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-03 01:48:17.303 [34mINFO [0;39m 22612 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-03 01:48:17.397 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 112 mappings in 'requestMappingHandlerMapping'
2025-08-03 01:48:17.405 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-03 01:48:17.802 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-03 01:48:17.945 [34mINFO [0;39m 22612 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-03 01:48:17.966 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-03 01:48:17.967 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-03 01:48:17.968 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-03 01:48:17.968 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-03 01:48:17.968 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-03 01:48:17.968 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-03 01:48:17.968 [39mDEBUG[0;39m 22612 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-03 01:48:17.968 [34mINFO [0;39m 22612 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7ed0e5db, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@38bce2ed, org.springframework.security.web.context.SecurityContextPersistenceFilter@3b09582d, org.springframework.security.web.header.HeaderWriterFilter@5166fe7a, org.springframework.web.filter.CorsFilter@5c91b751, org.springframework.security.web.authentication.logout.LogoutFilter@56114349, com.example.pure.filter.JwtFilter@2acbe46d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@29f72685, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f8488ef, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@38fc2c39, org.springframework.security.web.session.SessionManagementFilter@7a925ac4, org.springframework.security.web.access.ExceptionTranslationFilter@5c2f5dc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@70028163]
2025-08-03 01:48:18.009 [39mTRACE[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@213e3629, started on Sun Aug 03 01:48:11 CST 2025
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-03 01:48:18.029 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-03 01:48:18.030 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiRequestLogController:
	
2025-08-03 01:48:18.031 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-03 01:48:18.035 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
2025-08-03 01:48:18.036 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-03 01:48:18.036 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-03 01:48:18.036 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-03 01:48:18.036 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-03 01:48:18.038 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-03 01:48:18.039 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-03 01:48:18.039 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-03 01:48:18.039 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-03 01:48:18.143 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-03 01:48:18.181 [39mDEBUG[0;39m 22612 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-03 01:48:18.447 [34mINFO [0;39m 22612 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-03 01:48:18.460 [34mINFO [0;39m 22612 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 01:48:18.462 [39mDEBUG[0;39m 22612 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 01:48:18.462 [39mDEBUG[0;39m 22612 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 01:48:18.462 [34mINFO [0;39m 22612 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-03 01:48:18.462 [39mDEBUG[0;39m 22612 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3d70dab8]
2025-08-03 01:48:18.463 [39mDEBUG[0;39m 22612 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3d70dab8]
2025-08-03 01:48:18.463 [34mINFO [0;39m 22612 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3d70dab8]]
2025-08-03 01:48:18.463 [34mINFO [0;39m 22612 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-03 01:48:18.463 [39mDEBUG[0;39m 22612 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:48:18.463 [39mDEBUG[0;39m 22612 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 01:48:18.480 [34mINFO [0;39m 22612 --- [main] com.example.pure.PureApplication : Started PureApplication in 8.058 seconds (JVM running for 9.345)
2025-08-03 01:48:34.870 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 01:48:34.870 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Initializing Servlet 'dispatcherServlet'
2025-08-03 01:48:34.870 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected StandardServletMultipartResolver
2025-08-03 01:48:34.870 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected AcceptHeaderLocaleResolver
2025-08-03 01:48:34.870 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected FixedThemeResolver
2025-08-03 01:48:34.873 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@1e9e3356
2025-08-03 01:48:34.873 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Detected org.springframework.web.servlet.support.SessionFlashMapManager@3b95bf88
2025-08-03 01:48:34.874 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-08-03 01:48:34.874 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed initialization in 4 ms
2025-08-03 01:48:34.887 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Securing POST /api/ai/config/api-keys/batch
2025-08-03 01:48:34.890 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Set SecurityContextHolder to empty SecurityContext
2025-08-03 01:48:34.898 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:48:35.491 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 正在加载用户信息: admin
2025-08-03 01:48:35.549 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : id: 1,password: $2a$10$LKHG2SMd1qOj5tPMXJLRXOKNENpkfDFMGXUP4SngxFZgLn.9E9IMW
2025-08-03 01:48:35.554 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:48:35.559 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ed3263d]
2025-08-03 01:48:35.565 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@730595383 wrapping com.mysql.cj.jdbc.ConnectionImpl@34567b2f] will be managed by Spring
2025-08-03 01:48:35.567 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==>  Preparing: SELECT r.* FROM roles r JOIN user_roles ur ON r.id = ur.role_id WHERE ur.user_id = ?
2025-08-03 01:48:35.590 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : ==> Parameters: 1(Long)
2025-08-03 01:48:35.616 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.RoleMapper.findByUserId : <==      Total: 1
2025-08-03 01:48:35.618 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ed3263d]
2025-08-03 01:48:35.619 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.CustomUserDetailsService : 用户 admin 的角色信息已加载, 角色数量: 1
2025-08-03 01:48:35.620 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ed3263d]
2025-08-03 01:48:35.620 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ed3263d]
2025-08-03 01:48:35.620 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ed3263d]
2025-08-03 01:48:35.625 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] com.example.pure.filter.JwtFilter : 用户 'admin' 认证成功
2025-08-03 01:48:35.633 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.s.w.a.i.FilterSecurityInterceptor : Authorized filter invocation [POST /api/ai/config/api-keys/batch] with attributes [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')]
2025-08-03 01:48:35.634 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.security.web.FilterChainProxy : Secured POST /api/ai/config/api-keys/batch
2025-08-03 01:48:35.637 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : POST "/api/ai/config/api-keys/batch", parameters={}
2025-08-03 01:48:35.640 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.pure.controller.openai.AiConfigController#batchAddApiKeys(BatchAddApiKeyRequest, Authentication)
2025-08-03 01:48:35.663 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [BatchAddApiKeyRequest(configGroupId=3, apiKeys=[AIzaSyBRV9C_xUM3MNSFlWuVhrhIK3JMufmPr7I, AIzaSyBADRs (truncated)...]
2025-08-03 01:48:35.781 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥 - 用户ID: 1, 配置分组ID: 3, 数量: 3
2025-08-03 01:48:35.782 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Creating a new SqlSession
2025-08-03 01:48:35.783 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:35.783 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.m.s.t.SpringManagedTransaction : JDBC Connection [HikariProxyConnection@********** wrapping com.mysql.cj.jdbc.ConnectionImpl@34567b2f] will be managed by Spring
2025-08-03 01:48:35.783 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==>  Preparing: SELECT id, user_id, group_name, provider, custom_base_url, test_model, preferred_model, default_temperature, default_max_tokens, default_top_p, stream_enabled, timeout_seconds, system_prompt, created_at, updated_at FROM user_ai_config_groups WHERE user_id = ? ORDER BY created_at ASC
2025-08-03 01:48:35.783 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : ==> Parameters: 1(Long)
2025-08-03 01:48:35.788 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectByUserId : <==      Total: 1
2025-08-03 01:48:35.788 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:35.789 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:48:35.789 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:48:35.790 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:48:35.790 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:35.790 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBRV9...
2025-08-03 01:48:35.790 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:48:35.827 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:35.855 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [163b9926] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:36.353 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [163b9926] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:48:37.077 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [163b9926] [54a4ec7f-1] Response 200 OK
2025-08-03 01:48:37.096 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [163b9926] [54a4ec7f-1] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you tod (truncated)..."
2025-08-03 01:48:37.097 [34mINFO [0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 01:48:37.097 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156917,"id":"dU-OaNCvAqHQz7IP5bKNsAk","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":36}}
2025-08-03 01:48:37.097 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156917,"id":"dU-OaNCvAqHQz7IP5bKNsAk","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":36}}, created=null, model=null, choices=null, usage=null)
2025-08-03 01:48:37.120 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: Hi there! How can I help you today?
2025-08-03 01:48:37.121 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试成功 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应长度: 99
2025-08-03 01:48:37.122 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.122 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-03 01:48:37.123 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 3(Long), d7d81099330e6b959af48cb405d0135960453601478729b27c3be4a5302fde6a42918ba9f56f895d6c4202595882bb12242ad6b70171861954e3d4e963306046(String), true(Boolean), 1(Integer)
2025-08-03 01:48:37.127 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-03 01:48:37.130 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.130 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.130 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : ==>  Preparing: SELECT k.id, k.user_id, k.config_group_id, k.api_key_encrypted, k.is_active, k.priority, k.usage_count, k.last_used_at, k.created_at, k.updated_at, g.provider FROM user_api_keys k LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id WHERE k.id = ?
2025-08-03 01:48:37.130 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : ==> Parameters: 5(Long)
2025-08-03 01:48:37.133 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : <==      Total: 1
2025-08-03 01:48:37.133 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.137 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 5
2025-08-03 01:48:37.138 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.138 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:48:37.138 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 5(Long)
2025-08-03 01:48:37.144 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-03 01:48:37.145 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.146 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.146 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, is_healthy, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-03 01:48:37.147 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==> Parameters: 1(Long), 3(Long), 5(Long), 0(Integer), 0(Long), 0(Integer), true(Boolean), 2025-08-03 01:48:37.1465407(Timestamp)
2025-08-03 01:48:37.152 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : <==    Updates: 1
2025-08-03 01:48:37.153 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.153 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : API密钥负载状态初始化完成 - ID: 5
2025-08-03 01:48:37.153 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.153 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:48:37.154 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 5(Long)
2025-08-03 01:48:37.155 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 1
2025-08-03 01:48:37.156 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.156 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 3, 密钥: AIza****Pr7I
2025-08-03 01:48:37.156 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:48:37.156 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:48:37.156 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:48:37.156 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:37.156 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyBADR...
2025-08-03 01:48:37.156 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:48:37.157 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:37.158 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [26a80845] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:37.160 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [26a80845] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:48:37.863 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [26a80845] [54a4ec7f-2] Response 200 OK
2025-08-03 01:48:37.864 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [26a80845] [54a4ec7f-2] Decoded "{"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you tod (truncated)..."
2025-08-03 01:48:37.864 [34mINFO [0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 01:48:37.864 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156917,"id":"dU-OaNXjM-PVz7IP7ozNyAU","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":36}}
2025-08-03 01:48:37.865 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"stop","index":0,"message":{"content":"Hi there! How can I help you today?","role":"assistant"}}],"created":1754156917,"id":"dU-OaNXjM-PVz7IP7ozNyAU","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":10,"prompt_tokens":2,"total_tokens":36}}, created=null, model=null, choices=null, usage=null)
2025-08-03 01:48:37.865 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: Hi there! How can I help you today?
2025-08-03 01:48:37.865 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试成功 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应长度: 99
2025-08-03 01:48:37.865 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.865 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-03 01:48:37.866 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 3(Long), ca751010dccce45ed5c35f0c2b0f0a980bfc21a40c8b6be7073ee30880db32a6187f24e31dbb1db31993db1915ed4b992d13b995979a9d3c0810c56032d1e059(String), true(Boolean), 1(Integer)
2025-08-03 01:48:37.868 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-03 01:48:37.869 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.869 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.869 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : ==>  Preparing: SELECT k.id, k.user_id, k.config_group_id, k.api_key_encrypted, k.is_active, k.priority, k.usage_count, k.last_used_at, k.created_at, k.updated_at, g.provider FROM user_api_keys k LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id WHERE k.id = ?
2025-08-03 01:48:37.869 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : ==> Parameters: 6(Long)
2025-08-03 01:48:37.871 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : <==      Total: 1
2025-08-03 01:48:37.871 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.871 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 6
2025-08-03 01:48:37.872 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.872 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:48:37.872 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 6(Long)
2025-08-03 01:48:37.874 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-03 01:48:37.874 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.874 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.874 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, is_healthy, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-03 01:48:37.875 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==> Parameters: 1(Long), 3(Long), 6(Long), 0(Integer), 0(Long), 0(Integer), true(Boolean), 2025-08-03 01:48:37.8748949(Timestamp)
2025-08-03 01:48:37.877 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : <==    Updates: 1
2025-08-03 01:48:37.878 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.878 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : API密钥负载状态初始化完成 - ID: 6
2025-08-03 01:48:37.878 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:37.878 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:48:37.878 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 6(Long)
2025-08-03 01:48:37.880 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 1
2025-08-03 01:48:37.880 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:37.880 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 3, 密钥: AIza****ixyI
2025-08-03 01:48:37.880 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 发起非流式聊天请求 - 提供商: GOOGLE, 模型: gemini-2.5-flash
2025-08-03 01:48:37.880 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 使用用户自定义Google BaseURL: https://generativelanguage.googleapis.com/v1beta/openai
2025-08-03 01:48:37.880 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : === Google 非流式请求详情 ===
2025-08-03 01:48:37.881 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:37.881 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求头: Authorization=Bearer AIzaSyDKiH...
2025-08-03 01:48:37.881 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : 请求体: {stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5-flash}
2025-08-03 01:48:37.881 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求开始 - URL: https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:37.882 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions : [480d1b45] HTTP POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions
2025-08-03 01:48:37.883 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [480d1b45] Encoding [{stream=false, max_tokens=200, temperature=0.6, messages=[{role=user, content=Hi}], model=gemini-2.5 (truncated)...]
2025-08-03 01:48:39.599 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions : [480d1b45] [54a4ec7f-3] Response 200 OK
2025-08-03 01:48:39.601 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] org.springframework.web.HttpLogging : [480d1b45] [54a4ec7f-3] Decoded "{"choices":[{"finish_reason":"length","index":0,"message":{"content":"Hello","role":"assistant"}}]," (truncated)..."
2025-08-03 01:48:39.602 [34mINFO [0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google非流式请求完成
2025-08-03 01:48:39.602 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : Google直接转发原始数据: {"choices":[{"finish_reason":"length","index":0,"message":{"content":"Hello","role":"assistant"}}],"created":1754156919,"id":"d0-OaNu3I52gz7IPtdadyQQ","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":1,"prompt_tokens":2,"total_tokens":200}}
2025-08-03 01:48:39.602 [39mDEBUG[0;39m 22612 --- [reactor-http-nio-2] c.e.p.s.o.i.ModelAdapterServiceImpl : response:ChatCompletionResponse(id=RAWDATA, object={"choices":[{"finish_reason":"length","index":0,"message":{"content":"Hello","role":"assistant"}}],"created":1754156919,"id":"d0-OaNu3I52gz7IPtdadyQQ","model":"gemini-2.5-flash","object":"chat.completion","usage":{"completion_tokens":1,"prompt_tokens":2,"total_tokens":200}}, created=null, model=null, choices=null, usage=null)
2025-08-03 01:48:39.602 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.pure.config.ContentDeserializer : 反序列化字符串格式内容: Hello
2025-08-03 01:48:39.602 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥测试成功 - 提供商: GOOGLE, 模型: gemini-2.5-flash, 响应长度: 69
2025-08-03 01:48:39.602 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:39.602 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==>  Preparing: INSERT INTO user_api_keys ( user_id, config_group_id, api_key_encrypted, is_active, priority ) VALUES ( ?, ?, ?, ?, ? )
2025-08-03 01:48:39.603 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : ==> Parameters: 1(Long), 3(Long), df0adc3b478b0a687b982807fc17a4be869556b8c847447805b79bdb7087e543c243b37c5f2751157d8b080b6d2467df47b8674bfbfcf48ff8eb1ca7eff373a2(String), true(Boolean), 1(Integer)
2025-08-03 01:48:39.606 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.UserApiKeyMapper.insert : <==    Updates: 1
2025-08-03 01:48:39.606 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.606 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:39.606 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : ==>  Preparing: SELECT k.id, k.user_id, k.config_group_id, k.api_key_encrypted, k.is_active, k.priority, k.usage_count, k.last_used_at, k.created_at, k.updated_at, g.provider FROM user_api_keys k LEFT JOIN user_ai_config_groups g ON k.config_group_id = g.id WHERE k.id = ?
2025-08-03 01:48:39.607 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : ==> Parameters: 7(Long)
2025-08-03 01:48:39.609 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.U.selectById : <==      Total: 1
2025-08-03 01:48:39.609 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.610 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : 初始化API密钥负载状态 - ID: 7
2025-08-03 01:48:39.610 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:39.610 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:48:39.610 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 7(Long)
2025-08-03 01:48:39.612 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 0
2025-08-03 01:48:39.612 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.612 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:39.612 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==>  Preparing: INSERT INTO api_key_load_balance ( user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, is_healthy, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-03 01:48:39.613 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : ==> Parameters: 1(Long), 3(Long), 7(Long), 0(Integer), 0(Long), 0(Integer), true(Boolean), 2025-08-03 01:48:39.6124322(Timestamp)
2025-08-03 01:48:39.615 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.insert : <==    Updates: 1
2025-08-03 01:48:39.616 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.616 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.i.LoadBalancerServiceImpl : API密钥负载状态初始化完成 - ID: 7
2025-08-03 01:48:39.616 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6] from current transaction
2025-08-03 01:48:39.616 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==>  Preparing: SELECT id, user_id, config_group_id, api_key_id, current_requests, total_requests, error_count, last_error_at, is_healthy, updated_at FROM api_key_load_balance WHERE api_key_id = ?
2025-08-03 01:48:39.616 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : ==> Parameters: 7(Long)
2025-08-03 01:48:39.618 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.m.p.A.selectByApiKeyId : <==      Total: 1
2025-08-03 01:48:39.618 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.618 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : API密钥添加成功 - 用户ID: 1, 配置分组ID: 3, 密钥: AIza****wmUU
2025-08-03 01:48:39.618 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.s.o.impl.AiConfigServiceImpl : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 3, 成功: 3, 失败: 0
2025-08-03 01:48:39.618 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.618 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.618 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] org.mybatis.spring.SqlSessionUtils : Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@150fdcd6]
2025-08-03 01:48:39.635 [34mINFO [0;39m 22612 --- [http-nio-8080-exec-2] c.e.p.c.openai.AiConfigController : 批量添加API密钥完成 - 用户ID: 1, 配置分组ID: 3, 总数: 3, 成功: 3, 失败: 0
2025-08-03 01:48:39.649 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
2025-08-03 01:48:39.664 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor : Writing [Result(code=200, message=批量添加完成, success=true, data=BatchAddApiKeyResult(successKeys=[ApiKeyDto(id=5 (truncated)...]
2025-08-03 01:48:39.678 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet : Completed 200 OK
2025-08-03 01:48:39.679 [39mDEBUG[0;39m 22612 --- [http-nio-8080-exec-2] o.s.s.w.c.SecurityContextPersistenceFilter : Cleared SecurityContextHolder to complete request
2025-08-03 01:49:17.994 [34mINFO [0;39m 22612 --- [MessageBroker-1] o.s.w.s.c.WebSocketMessageBrokerStats : WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-03 02:05:54.842 [39mDEBUG[0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel removed SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 02:05:54.842 [39mDEBUG[0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 02:05:54.842 [34mINFO [0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopping...
2025-08-03 02:05:54.842 [34mINFO [0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3d70dab8]]
2025-08-03 02:05:54.842 [39mDEBUG[0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3d70dab8]
2025-08-03 02:05:54.842 [39mDEBUG[0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3d70dab8]
2025-08-03 02:05:54.843 [34mINFO [0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.b.SimpleBrokerMessageHandler : Stopped.
2025-08-03 02:05:54.843 [39mDEBUG[0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 02:05:54.843 [39mDEBUG[0;39m 22612 --- [SpringApplicationShutdownHook] o.s.m.s.ExecutorSubscribableChannel : brokerChannel removed UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 02:05:57.077 [34mINFO [0;39m 22612 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown initiated...
2025-08-03 02:05:57.085 [34mINFO [0;39m 22612 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Shutdown completed.
2025-08-03 02:06:03.084 [34mINFO [0;39m 1304 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-08-03 02:06:03.090 [34mINFO [0;39m 1304 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 1304 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-08-03 02:06:03.091 [39mDEBUG[0;39m 1304 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-03 02:06:03.091 [34mINFO [0;39m 1304 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-08-03 02:06:04.144 [34mINFO [0;39m 1304 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-03 02:06:04.147 [34mINFO [0;39m 1304 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-03 02:06:04.202 [34mINFO [0;39m 1304 --- [main] o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-08-03 02:06:04.335 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\AccessLogMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ApiKeyLoadBalanceMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\ChatSessionMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\CompatibleApiKeyMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\MessagesMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OAuth2Mapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OpenAiRequestLogMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\OperatingLogMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoEpisodesMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInfoTypeLinkMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoInteractionMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoTypeMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\PureVideoUrlMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\RoleMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserAiConfigMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserApiKeyMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserProfileMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\UserRoleMapper.class]
2025-08-03 02:06:04.336 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\mapper\primary\VideoCommentInteractionMapper.class]
2025-08-03 02:06:04.337 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'accessLogMapper' and 'com.example.pure.mapper.primary.AccessLogMapper' mapperInterface
2025-08-03 02:06:04.339 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'accessLogMapper'.
2025-08-03 02:06:04.340 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'apiKeyLoadBalanceMapper' and 'com.example.pure.mapper.primary.ApiKeyLoadBalanceMapper' mapperInterface
2025-08-03 02:06:04.340 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'apiKeyLoadBalanceMapper'.
2025-08-03 02:06:04.340 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'chatSessionMapper' and 'com.example.pure.mapper.primary.ChatSessionMapper' mapperInterface
2025-08-03 02:06:04.341 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'chatSessionMapper'.
2025-08-03 02:06:04.341 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'compatibleApiKeyMapper' and 'com.example.pure.mapper.primary.CompatibleApiKeyMapper' mapperInterface
2025-08-03 02:06:04.341 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'compatibleApiKeyMapper'.
2025-08-03 02:06:04.341 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'messagesMapper' and 'com.example.pure.mapper.primary.MessagesMapper' mapperInterface
2025-08-03 02:06:04.341 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'messagesMapper'.
2025-08-03 02:06:04.341 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'OAuth2Mapper' and 'com.example.pure.mapper.primary.OAuth2Mapper' mapperInterface
2025-08-03 02:06:04.342 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'OAuth2Mapper'.
2025-08-03 02:06:04.342 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'openAiRequestLogMapper' and 'com.example.pure.mapper.primary.OpenAiRequestLogMapper' mapperInterface
2025-08-03 02:06:04.342 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'openAiRequestLogMapper'.
2025-08-03 02:06:04.342 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'operatingLogMapper' and 'com.example.pure.mapper.primary.OperatingLogMapper' mapperInterface
2025-08-03 02:06:04.343 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'operatingLogMapper'.
2025-08-03 02:06:04.343 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoEpisodesMapper' and 'com.example.pure.mapper.primary.PureVideoEpisodesMapper' mapperInterface
2025-08-03 02:06:04.343 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoEpisodesMapper'.
2025-08-03 02:06:04.343 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper' and 'com.example.pure.mapper.primary.PureVideoInfoTypeLinkMapper' mapperInterface
2025-08-03 02:06:04.344 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInfoTypeLinkMapper'.
2025-08-03 02:06:04.344 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoInteractionMapper' and 'com.example.pure.mapper.primary.PureVideoInteractionMapper' mapperInterface
2025-08-03 02:06:04.344 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoInteractionMapper'.
2025-08-03 02:06:04.344 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoTypeMapper' and 'com.example.pure.mapper.primary.PureVideoTypeMapper' mapperInterface
2025-08-03 02:06:04.344 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoTypeMapper'.
2025-08-03 02:06:04.345 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'pureVideoUrlMapper' and 'com.example.pure.mapper.primary.PureVideoUrlMapper' mapperInterface
2025-08-03 02:06:04.345 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'pureVideoUrlMapper'.
2025-08-03 02:06:04.345 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'roleMapper' and 'com.example.pure.mapper.primary.RoleMapper' mapperInterface
2025-08-03 02:06:04.345 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'roleMapper'.
2025-08-03 02:06:04.345 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userAiConfigMapper' and 'com.example.pure.mapper.primary.UserAiConfigMapper' mapperInterface
2025-08-03 02:06:04.346 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userAiConfigMapper'.
2025-08-03 02:06:04.346 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userApiKeyMapper' and 'com.example.pure.mapper.primary.UserApiKeyMapper' mapperInterface
2025-08-03 02:06:04.346 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userApiKeyMapper'.
2025-08-03 02:06:04.346 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userMapper' and 'com.example.pure.mapper.primary.UserMapper' mapperInterface
2025-08-03 02:06:04.347 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-08-03 02:06:04.347 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userProfileMapper' and 'com.example.pure.mapper.primary.UserProfileMapper' mapperInterface
2025-08-03 02:06:04.347 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userProfileMapper'.
2025-08-03 02:06:04.347 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'userRoleMapper' and 'com.example.pure.mapper.primary.UserRoleMapper' mapperInterface
2025-08-03 02:06:04.347 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'userRoleMapper'.
2025-08-03 02:06:04.347 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Creating MapperFactoryBean with name 'videoCommentInteractionMapper' and 'com.example.pure.mapper.primary.VideoCommentInteractionMapper' mapperInterface
2025-08-03 02:06:04.348 [39mDEBUG[0;39m 1304 --- [main] o.m.s.mapper.ClassPathMapperScanner : Enabling autowire by type for MapperFactoryBean with name 'videoCommentInteractionMapper'.
2025-08-03 02:06:05.025 [34mINFO [0;39m 1304 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8080 (http)
2025-08-03 02:06:05.031 [34mINFO [0;39m 1304 --- [main] o.a.coyote.http11.Http11NioProtocol : Initializing ProtocolHandler ["http-nio-8080"]
2025-08-03 02:06:05.032 [34mINFO [0;39m 1304 --- [main] o.a.catalina.core.StandardService : Starting service [Tomcat]
2025-08-03 02:06:05.032 [34mINFO [0;39m 1304 --- [main] o.a.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-03 02:06:05.131 [34mINFO [0;39m 1304 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/] : Initializing Spring embedded WebApplicationContext
2025-08-03 02:06:05.131 [34mINFO [0;39m 1304 --- [main] o.s.b.w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1993 ms
2025-08-03 02:06:05.433 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\AccessLogMapper.xml]'
2025-08-03 02:06:05.443 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ApiKeyLoadBalanceMapper.xml]'
2025-08-03 02:06:05.453 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\ChatSessionMapper.xml]'
2025-08-03 02:06:05.461 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\CompatibleApiKeyMapper.xml]'
2025-08-03 02:06:05.469 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\MessagesMapper.xml]'
2025-08-03 02:06:05.473 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OAuth2Mapper.xml]'
2025-08-03 02:06:05.484 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\OperatingLogMapper.xml]'
2025-08-03 02:06:05.495 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoEpisodesMapper.xml]'
2025-08-03 02:06:05.499 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInfoTypeLinkMapper.xml]'
2025-08-03 02:06:05.504 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoInteractionMapper.xml]'
2025-08-03 02:06:05.507 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoTypeMapper.xml]'
2025-08-03 02:06:05.512 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\PureVideoUrlMapper.xml]'
2025-08-03 02:06:05.519 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\RoleMapper.xml]'
2025-08-03 02:06:05.526 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserAiConfigMapper.xml]'
2025-08-03 02:06:05.532 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserApiKeyMapper.xml]'
2025-08-03 02:06:05.541 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserMapper.xml]'
2025-08-03 02:06:05.545 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserProfileMapper.xml]'
2025-08-03 02:06:05.556 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\UserRoleMapper.xml]'
2025-08-03 02:06:05.561 [39mDEBUG[0;39m 1304 --- [main] o.m.spring.SqlSessionFactoryBean : Parsed mapper file: 'file [C:\MyHappy\Best\myapp\pure\target\classes\mapper\VideoCommentInteractionMapper.xml]'
2025-08-03 02:06:05.574 [34mINFO [0;39m 1304 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Starting...
2025-08-03 02:06:05.904 [34mINFO [0;39m 1304 --- [main] com.zaxxer.hikari.HikariDataSource : HikariPool-1 - Start completed.
2025-08-03 02:06:06.510 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.r.ResourceUrlEncodingFilter : Filter 'resourceUrlEncodingFilter' configured for use
2025-08-03 02:06:06.511 [39mDEBUG[0;39m 1304 --- [main] com.example.pure.filter.JwtFilter : Filter 'jwtFilter' configured for use
2025-08-03 02:06:06.909 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 02:06:06.911 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.auth.OperatingLogController; public com.example.pure.common.Result com.example.pure.controller.auth.OperatingLogController.getCurrentUserOperationLogs(org.springframework.security.core.Authentication,com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.450 [34mINFO [0;39m 1304 --- [main] com.example.pure.config.R2Config : 初始化S3CrtAsyncClient - 启用高性能CRT优化
2025-08-03 02:06:07.598 [34mINFO [0;39m 1304 --- [main] com.example.pure.config.R2Config : 初始化S3TransferManager - 启用完全自动优化策略
2025-08-03 02:06:07.606 [34mINFO [0;39m 1304 --- [main] com.example.pure.config.R2Config : S3TransferManager初始化完成 - 自动优化：8MB阈值分片上传，智能并发控制，断点续传
2025-08-03 02:06:07.667 [34mINFO [0;39m 1304 --- [main] c.e.pure.config.SnowflakeConfig : 初始化雪花算法ID生成器 - 机器ID: 1, 数据中心ID: 1
2025-08-03 02:06:07.668 [34mINFO [0;39m 1304 --- [main] c.e.pure.util.SnowflakeIdGenerator : 雪花算法ID生成器初始化 - 机器ID: 1, 数据中心ID: 1
2025-08-03 02:06:07.682 [34mINFO [0;39m 1304 --- [main] c.e.pure.util.SpringEncryptionUtil : Spring加密工具初始化成功（CBC模式）
2025-08-03 02:06:07.715 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)
2025-08-03 02:06:07.715 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUserWithUserProfileByUsername(javax.servlet.http.HttpServletRequest,org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasRole('ADMIN') or isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.716 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#id)") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)
2025-08-03 02:06:07.717 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUser(java.lang.Long,javax.servlet.http.HttpServletRequest)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#id)', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.717 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)
2025-08-03 02:06:07.717 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getUsersByPage(com.example.pure.model.dto.request.page.PageRequestDTO)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.718 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("isAuthenticated()") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 02:06:07.718 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getCurrentUser(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'isAuthenticated()', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.718 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()
2025-08-03 02:06:07.718 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.getAllUsers()]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.718 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)
2025-08-03 02:06:07.718 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.user.UserController; public com.example.pure.common.Result com.example.pure.controller.user.UserController.deleteUser(java.lang.Long)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.723 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasAnyRole(\'COMMON\', \'ADMIN\')") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)
2025-08-03 02:06:07.723 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.getUserProfileByUsername(org.springframework.security.core.userdetails.UserDetails)]] with attributes [[authorize: 'hasAnyRole('COMMON', 'ADMIN')', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.724 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.p.PrePostAnnotationSecurityMetadataSource : @org.springframework.security.access.prepost.PreAuthorize("hasRole(\'ADMIN\') or @securityService.isCurrentUser(#userProfile.getUsername())") found on specific method: public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)
2025-08-03 02:06:07.724 [39mDEBUG[0;39m 1304 --- [main] o.s.s.a.m.DelegatingMethodSecurityMetadataSource : Caching method [CacheKey[com.example.pure.controller.userprofile.UserProfileController; public com.example.pure.common.Result com.example.pure.controller.userprofile.UserProfileController.updateUserProfile(com.example.pure.model.entity.UserProfile)]] with attributes [[authorize: 'hasRole('ADMIN') or @securityService.isCurrentUser(#userProfile.getUsername())', filter: 'null', filterTarget: 'null']]
2025-08-03 02:06:07.848 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for EndpointRequestMatcher includes=[health, info, prometheus, loggers, threaddump], excludes=[], includeLinks=false
2025-08-03 02:06:07.849 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true
2025-08-03 02:06:07.852 [34mINFO [0;39m 1304 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure EndpointRequestMatcher includes=[*], excludes=[], includeLinks=true with [org.springframework.security.web.session.DisableEncodeUrlFilter@5e1d3188, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5982255e, org.springframework.security.web.context.SecurityContextPersistenceFilter@6cbb79c3, org.springframework.security.web.header.HeaderWriterFilter@4b6ad2eb, org.springframework.security.web.authentication.logout.LogoutFilter@680408ac, com.example.pure.filter.JwtFilter@5e6bbe63, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@409b1740, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@32dc895e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7306836f, org.springframework.security.web.session.SessionManagementFilter@a23b96b, org.springframework.security.web.access.ExceptionTranslationFilter@72364a40, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3eb29382]
2025-08-03 02:06:07.854 [34mINFO [0;39m 1304 --- [main] com.example.pure.config.AsyncConfig : 创建CPU密集型异步任务线程池
2025-08-03 02:06:07.856 [34mINFO [0;39m 1304 --- [main] com.example.pure.config.AsyncConfig : 创建文件操作异步任务线程池
2025-08-03 02:06:07.857 [34mINFO [0;39m 1304 --- [main] com.example.pure.config.AsyncConfig : 通用异步任务线程池初始化完成 - 核心线程数: 6, 最大线程数: 10
2025-08-03 02:06:07.857 [34mINFO [0;39m 1304 --- [main] com.example.pure.config.AsyncConfig : 创建 SSE 专用异步任务线程池
2025-08-03 02:06:08.050 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.s.s.WebSocketHandlerMapping : Patterns [/ws/**, /ws-raw] in 'stompWebSocketHandlerMapping'
2025-08-03 02:06:08.068 [34mINFO [0;39m 1304 --- [main] o.s.b.a.w.s.WelcomePageHandlerMapping : Adding welcome page: class path resource [static/index.html]
2025-08-03 02:06:08.133 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerMapping : 112 mappings in 'requestMappingHandlerMapping'
2025-08-03 02:06:08.141 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/, /swagger-ui/] in 'viewControllerHandlerMapping'
2025-08-03 02:06:08.540 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.h.SimpleUrlHandlerMapping : Patterns [/webjars/**, /**, /static/**, /swagger-ui/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-03 02:06:08.687 [34mINFO [0;39m 1304 --- [main] o.s.b.a.e.web.EndpointLinksResolver : Exposing 16 endpoint(s) beneath base path '/actuator'
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/login']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/password']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/createUser']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/refresh/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/auth/captcha']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/public/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrcode/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/qrlogin/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/ws-raw/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/render/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/callback/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/oauth/refresh/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/verification/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/user/update/password']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/videoUrl/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video/**']
2025-08-03 02:06:08.708 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/video-legacy/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/image-legacy/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/download-legacy/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai-chat/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/api/ai']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v1/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/favicon.ico']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/error']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/*.html']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.html']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.css']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**/*.js']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/static/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/doc.html']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/openapi.json']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/v2/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [permitAll] for Ant [pattern='/**', OPTIONS]
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/user/get/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/profile/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasAnyRole('ROLE_USER','ROLE_ADMIN','ROLE_COMMON')] for Ant [pattern='/api/ai/config/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/admin/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [hasRole('ROLE_ADMIN')] for Ant [pattern='/api/management/**']
2025-08-03 02:06:08.709 [39mDEBUG[0;39m 1304 --- [main] o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource : Adding web access control expression [authenticated] for any request
2025-08-03 02:06:08.710 [34mINFO [0;39m 1304 --- [main] o.s.s.web.DefaultSecurityFilterChain : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@41f274d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b90a402, org.springframework.security.web.context.SecurityContextPersistenceFilter@50c99398, org.springframework.security.web.header.HeaderWriterFilter@20673498, org.springframework.web.filter.CorsFilter@67fdc1e9, org.springframework.security.web.authentication.logout.LogoutFilter@183e8c8a, com.example.pure.filter.JwtFilter@5e6bbe63, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@cc4a0dd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@159e9629, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33a38f07, org.springframework.security.web.session.SessionManagementFilter@7a8d6eb3, org.springframework.security.web.access.ExceptionTranslationFilter@7a14d8a4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@348c00d9]
2025-08-03 02:06:08.746 [39mTRACE[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : Looking for @MessageExceptionHandler mappings: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@329a1243, started on Sun Aug 03 02:06:03 CST 2025
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.AuthController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OAuth2Controller:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.OperatingLogController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRCodeController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.QRLoginController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.a.VerificationController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.s.R2Controller:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.DownloadController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.FileManagerController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.ImageController:
	
2025-08-03 02:06:08.763 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureFileManagerController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.PureImageFileController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.f.u.VideoController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.m.MessagesController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiChatController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiConfigController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.AiSystemTestController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiCompatibleController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.o.OpenAiRequestLogController:
	
2025-08-03 02:06:08.764 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserController:
	
2025-08-03 02:06:08.767 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.WebSocketTestController:
	{[MESSAGE],[/private-message]}: privateMessage(Map,SimpMessageHeaderAccessor,Principal)
	{[MESSAGE],[/chat]}: handleChatMessage(WebSocketMessage)
	{[MESSAGE],[/echo]}: echo(String)
	{[MESSAGE],[/message]}: broadcastMessage(Map)
	{[MESSAGE],[/room/{roomId}]}: sendToRoom(String,Map)
2025-08-03 02:06:08.767 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.u.UserProfileController:
	
2025-08-03 02:06:08.768 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoInteractionController:
	
2025-08-03 02:06:08.768 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.PureVideoUrlController:
	
2025-08-03 02:06:08.768 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	c.e.p.c.v.VideoCommentInteractionController:
	
2025-08-03 02:06:08.768 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-08-03 02:06:08.769 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-08-03 02:06:08.769 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-08-03 02:06:08.769 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.WebSocketAnnotationMethodMessageHandler : 
	o.s.w.u.SwaggerConfigResource:
	
2025-08-03 02:06:08.863 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-03 02:06:08.898 [39mDEBUG[0;39m 1304 --- [main] o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-03 02:06:09.144 [34mINFO [0;39m 1304 --- [main] o.a.coyote.http11.Http11NioProtocol : Starting ProtocolHandler ["http-nio-8080"]
2025-08-03 02:06:09.155 [34mINFO [0;39m 1304 --- [main] o.s.b.w.e.tomcat.TomcatWebServer : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 02:06:09.156 [39mDEBUG[0;39m 1304 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientOutboundChannel added SubProtocolWebSocketHandler[StompSubProtocolHandler[v10.stomp, v11.stomp, v12.stomp]]
2025-08-03 02:06:09.156 [39mDEBUG[0;39m 1304 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added WebSocketAnnotationMethodMessageHandler[prefixes=[/app/]]
2025-08-03 02:06:09.157 [34mINFO [0;39m 1304 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Starting...
2025-08-03 02:06:09.157 [39mDEBUG[0;39m 1304 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7207cb8b]
2025-08-03 02:06:09.157 [39mDEBUG[0;39m 1304 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7207cb8b]
2025-08-03 02:06:09.157 [34mINFO [0;39m 1304 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7207cb8b]]
2025-08-03 02:06:09.157 [34mINFO [0;39m 1304 --- [main] o.s.m.s.b.SimpleBrokerMessageHandler : Started.
2025-08-03 02:06:09.157 [39mDEBUG[0;39m 1304 --- [main] o.s.m.s.ExecutorSubscribableChannel : clientInboundChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 02:06:09.157 [39mDEBUG[0;39m 1304 --- [main] o.s.m.s.ExecutorSubscribableChannel : brokerChannel added UserDestinationMessageHandler[DefaultUserDestinationResolver[prefix=/user/]]
2025-08-03 02:06:09.170 [34mINFO [0;39m 1304 --- [main] com.example.pure.PureApplication : Started PureApplication in 6.549 seconds (JVM running for 7.325)
