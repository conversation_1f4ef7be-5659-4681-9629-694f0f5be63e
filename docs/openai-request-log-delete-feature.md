# OpenAI请求日志删除功能实现文档

## 概述

本文档描述了为OpenAI请求日志系统添加根据ID删除数据库日志功能的完整实现过程。该功能提供了逻辑删除和物理删除两种删除方式，确保数据管理的灵活性和安全性。

## 功能特性

### 1. 删除方式
- ✅ **逻辑删除** - 将`is_deleted`字段设置为1，保留数据但标记为已删除
- ✅ **物理删除** - 从数据库中彻底删除记录

### 2. 安全特性
- ✅ **异常处理** - 完善的错误处理和日志记录
- ✅ **状态检查** - 逻辑删除时检查记录是否已被删除
- ✅ **操作日志** - 详细记录删除操作的过程和结果

### 3. API设计
- ✅ **RESTful风格** - 使用标准的DELETE HTTP方法
- ✅ **清晰的响应** - 明确的成功/失败状态和错误信息
- ✅ **路径区分** - 不同的URL路径区分逻辑删除和物理删除

## 实现架构

### 1. 数据访问层 (Mapper)

#### OpenaiRequestLogMapper接口
```java
/**
 * 根据ID删除请求日志（逻辑删除）
 */
int deleteById(@Param("id") Long id);

/**
 * 根据ID物理删除请求日志
 */
int deleteByIdPhysically(@Param("id") Long id);
```

#### OpenaiRequestLogMapper.xml
```xml
<!-- 逻辑删除：更新is_deleted字段 -->
<update id="deleteById">
    UPDATE request_logs 
    SET is_deleted = 1, updated_at = NOW()
    WHERE id = #{id} AND is_deleted = 0
</update>

<!-- 物理删除：从数据库中删除记录 -->
<delete id="deleteByIdPhysically">
    DELETE FROM request_logs 
    WHERE id = #{id}
</delete>
```

### 2. 服务层 (Service)

#### OpenaiRequestLogService
```java
/**
 * 逻辑删除请求日志
 */
public boolean deleteRequestLogById(Long id) {
    log.info("删除OpenAI请求日志 - 日志ID: {}", id);
    
    try {
        int affectedRows = openaiRequestLogMapper.deleteById(id);
        if (affectedRows > 0) {
            log.info("成功删除OpenAI请求日志 - 日志ID: {}", id);
            return true;
        } else {
            log.warn("删除OpenAI请求日志失败，日志不存在或已被删除 - 日志ID: {}", id);
            return false;
        }
    } catch (Exception e) {
        log.error("删除OpenAI请求日志异常 - 日志ID: {} | 错误: {}", id, e.getMessage(), e);
        return false;
    }
}

/**
 * 物理删除请求日志
 */
public boolean deleteRequestLogByIdPhysically(Long id) {
    log.info("物理删除OpenAI请求日志 - 日志ID: {}", id);
    
    try {
        int affectedRows = openaiRequestLogMapper.deleteByIdPhysically(id);
        if (affectedRows > 0) {
            log.info("成功物理删除OpenAI请求日志 - 日志ID: {}", id);
            return true;
        } else {
            log.warn("物理删除OpenAI请求日志失败，日志不存在 - 日志ID: {}", id);
            return false;
        }
    } catch (Exception e) {
        log.error("物理删除OpenAI请求日志异常 - 日志ID: {} | 错误: {}", id, e.getMessage(), e);
        return false;
    }
}
```

### 3. 控制器层 (Controller)

#### OpenaiRequestLogController
```java
/**
 * 逻辑删除OpenAI请求日志
 */
@DeleteMapping("/{id}")
public ResponseEntity<String> deleteOpenaiRequestLog(@PathVariable Long id) {
    log.info("收到删除OpenAI请求日志请求 - 日志ID: {}", id);
    
    try {
        boolean success = openaiRequestLogService.deleteRequestLogById(id);
        if (success) {
            log.info("成功删除OpenAI请求日志 - 日志ID: {}", id);
            return ResponseEntity.ok("删除成功");
        } else {
            log.warn("删除OpenAI请求日志失败，日志不存在或已被删除 - 日志ID: {}", id);
            return ResponseEntity.status(404).body("日志不存在或已被删除");
        }
    } catch (Exception e) {
        log.error("删除OpenAI请求日志异常 - 日志ID: {}", id, e);
        return ResponseEntity.status(500).body("删除失败: " + e.getMessage());
    }
}

/**
 * 物理删除OpenAI请求日志
 */
@DeleteMapping("/{id}/physically")
public ResponseEntity<String> deleteOpenaiRequestLogPhysically(@PathVariable Long id) {
    log.info("收到物理删除OpenAI请求日志请求 - 日志ID: {}", id);
    
    try {
        boolean success = openaiRequestLogService.deleteRequestLogByIdPhysically(id);
        if (success) {
            log.info("成功物理删除OpenAI请求日志 - 日志ID: {}", id);
            return ResponseEntity.ok("物理删除成功");
        } else {
            log.warn("物理删除OpenAI请求日志失败，日志不存在 - 日志ID: {}", id);
            return ResponseEntity.status(404).body("日志不存在");
        }
    } catch (Exception e) {
        log.error("物理删除OpenAI请求日志异常 - 日志ID: {}", id, e);
        return ResponseEntity.status(500).body("物理删除失败: " + e.getMessage());
    }
}
```

## API接口文档

### 1. 逻辑删除日志
```
DELETE /api/logs/openai-requests/{id}
```

**参数:**
- `id` (Long) - 日志ID

**响应:**
- `200 OK` - 删除成功
- `404 Not Found` - 日志不存在或已被删除
- `500 Internal Server Error` - 删除失败

**示例:**
```bash
curl -X DELETE http://localhost:8080/api/logs/openai-requests/123
```

**成功响应:**
```
删除成功
```

**失败响应:**
```
日志不存在或已被删除
```

### 2. 物理删除日志
```
DELETE /api/logs/openai-requests/{id}/physically
```

**参数:**
- `id` (Long) - 日志ID

**响应:**
- `200 OK` - 物理删除成功
- `404 Not Found` - 日志不存在
- `500 Internal Server Error` - 物理删除失败

**示例:**
```bash
curl -X DELETE http://localhost:8080/api/logs/openai-requests/123/physically
```

**成功响应:**
```
物理删除成功
```

**失败响应:**
```
日志不存在
```

## 删除逻辑说明

### 1. 逻辑删除
- **操作**: 将`is_deleted`字段设置为1，同时更新`updated_at`时间戳
- **条件**: 只删除`is_deleted = 0`的记录（避免重复删除）
- **优势**: 数据可恢复，保留历史记录
- **适用场景**: 日常数据管理，误删恢复

### 2. 物理删除
- **操作**: 从数据库中彻底删除记录
- **条件**: 直接根据ID删除，不检查`is_deleted`状态
- **优势**: 彻底清理数据，释放存储空间
- **适用场景**: 数据清理，隐私保护

## 安全考虑

### 1. 权限控制
- 建议添加权限验证，确保只有授权用户可以删除日志
- 可以根据用户角色限制删除权限

### 2. 操作审计
- 所有删除操作都有详细的日志记录
- 记录操作者、操作时间、操作结果

### 3. 数据备份
- 建议在物理删除前进行数据备份
- 重要数据建议使用逻辑删除而非物理删除

## 日志记录示例

### 成功删除
```
2024-01-15 10:30:15 INFO  - 收到删除OpenAI请求日志请求 - 日志ID: 123
2024-01-15 10:30:15 INFO  - 删除OpenAI请求日志 - 日志ID: 123
2024-01-15 10:30:15 INFO  - 成功删除OpenAI请求日志 - 日志ID: 123
```

### 删除失败
```
2024-01-15 10:35:20 INFO  - 收到删除OpenAI请求日志请求 - 日志ID: 999
2024-01-15 10:35:20 INFO  - 删除OpenAI请求日志 - 日志ID: 999
2024-01-15 10:35:20 WARN  - 删除OpenAI请求日志失败，日志不存在或已被删除 - 日志ID: 999
```

### 删除异常
```
2024-01-15 10:40:10 INFO  - 收到删除OpenAI请求日志请求 - 日志ID: 456
2024-01-15 10:40:10 INFO  - 删除OpenAI请求日志 - 日志ID: 456
2024-01-15 10:40:10 ERROR - 删除OpenAI请求日志异常 - 日志ID: 456 | 错误: Connection timeout
```

## 总结

通过本次实现，OpenAI请求日志系统现在具备了：

- ✅ **完整的删除功能** - 支持逻辑删除和物理删除
- ✅ **RESTful API设计** - 标准的HTTP DELETE方法
- ✅ **完善的错误处理** - 详细的错误信息和状态码
- ✅ **详细的日志记录** - 完整的操作审计轨迹
- ✅ **安全的删除逻辑** - 防止重复删除和数据不一致
- ✅ **灵活的删除方式** - 根据需求选择逻辑删除或物理删除

这为系统提供了完整的日志生命周期管理能力，支持数据清理、隐私保护和存储优化等需求。
