-- 创建请求日志表
CREATE TABLE `request_logs` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_id` VARCHAR(50) NOT NULL COMMENT '请求ID（用于追踪）',
    `user_id` BIGINT NULL COMMENT '用户ID',
    `start_time` DATETIME NOT NULL COMMENT '请求开始时间',
    `end_time` DATETIME NULL COMMENT '请求结束时间',
    `status` VARCHAR(20) NOT NULL COMMENT '请求状态（成功/失败）',
    `status_code` INT NULL COMMENT 'HTTP状态码',
    `request_type` VARCHAR(20) NOT NULL COMMENT '请求类型（stream/non-stream）',
    `duration_ms` BIGINT NULL COMMENT '花费时间（毫秒）',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `group_name` VARCHAR(100) NULL COMMENT '配置分组名称',
    `provider` VARCHAR(50) NOT NULL COMMENT 'AI提供商',
    `masked_api_key` VARCHAR(50) NULL COMMENT '使用的API密钥（脱敏）',
    `error_message` TEXT NULL COMMENT '错误信息（失败时）',
    `actual_base_url` VARCHAR(500) NULL COMMENT 'WebClient请求的实际AI大模型基础路径',
    `path_suffix` VARCHAR(100) NOT NULL COMMENT '请求路径后缀',
    `model_name` VARCHAR(100) NOT NULL COMMENT '请求的模型名称',
    `extra_info` TEXT NULL COMMENT '额外信息（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_request_id` (`request_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_provider` (`provider`),
    KEY `idx_status` (`status`),
    KEY `idx_request_type` (`request_type`),
    KEY `idx_model_name` (`model_name`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_duration_ms` (`duration_ms`),
    KEY `idx_user_provider_time` (`user_id`, `provider`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='请求日志表';

-- 创建索引优化查询性能
-- 复合索引：用户+时间范围查询
CREATE INDEX `idx_user_time_range` ON `request_logs` (`user_id`, `start_time`, `end_time`);

-- 复合索引：状态+时间查询（用于查询失败日志）
CREATE INDEX `idx_status_time` ON `request_logs` (`status`, `created_at`);

-- 复合索引：提供商+模型+时间查询
CREATE INDEX `idx_provider_model_time` ON `request_logs` (`provider`, `model_name`, `created_at`);

-- 分区表（可选，用于大数据量场景）
-- 按月分区，提高查询性能
-- ALTER TABLE `request_logs` PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
--     PARTITION p202401 VALUES LESS THAN (202402),
--     PARTITION p202402 VALUES LESS THAN (202403),
--     PARTITION p202403 VALUES LESS THAN (202404),
--     PARTITION p202404 VALUES LESS THAN (202405),
--     PARTITION p202405 VALUES LESS THAN (202406),
--     PARTITION p202406 VALUES LESS THAN (202407),
--     PARTITION p202407 VALUES LESS THAN (202408),
--     PARTITION p202408 VALUES LESS THAN (202409),
--     PARTITION p202409 VALUES LESS THAN (202410),
--     PARTITION p202410 VALUES LESS THAN (202411),
--     PARTITION p202411 VALUES LESS THAN (202412),
--     PARTITION p202412 VALUES LESS THAN (202501),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 示例数据插入（用于测试）
INSERT INTO `request_logs` (
    `request_id`, `user_id`, `start_time`, `end_time`, `status`, `status_code`,
    `request_type`, `duration_ms`, `retry_count`, `group_name`, `provider`,
    `masked_api_key`, `actual_base_url`, `path_suffix`, `model_name`
) VALUES
(
    'req_test001', 1, '2024-01-15 10:30:15', '2024-01-15 10:30:18',
    '成功', 200, 'non-stream', 3250, 0, '默认分组', 'OPENAI',
    'sk-1****abcd', 'https://api.openai.com/v1', '/chat/completions', 'gpt-4'
),
(
    'req_test002', 1, '2024-01-15 10:35:20', '2024-01-15 10:35:25',
    '失败', 500, 'stream', 5000, 2, '默认分组', 'OPENAI',
    'sk-1****abcd', 'https://api.openai.com/v1', '/chat/completions', 'gpt-4'
),
(
    'req_test003', 1, '2024-01-15 10:40:10', '2024-01-15 10:40:25',
    '成功', 200, 'non-stream', 15000, 0, '图片生成分组', 'OPENAI',
    'sk-1****abcd', 'https://api.openai.com/v1', '/images/generations', 'dall-e-3'
);
