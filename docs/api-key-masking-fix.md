# API密钥脱敏问题修复

## 问题描述

在 `OpenAiCompatibleServiceImpl.selectApiKeyFromConfigGroup` 方法中，调用 `aiConfigService.getUserApiKeysByConfigGroupId()` 时出现解密错误：

```
java.lang.IllegalArgumentException: Detected a Non-hex character at 1 or 2 position
```

## 根本原因分析

### 问题链路追踪

1. **调用链**：
   ```
   OpenAiCompatibleServiceImpl.selectApiKeyFromConfigGroup()
   → selectBestApiKeyFromGroup()
   → aiConfigService.getUserApiKeysByConfigGroupId()
   → maskApiKeyForDisplay() // ❌ 问题所在
   ```

2. **问题根源**：
   - `AiConfigServiceImpl.getUserApiKeysByConfigGroupId()` 方法中调用了 `maskApiKeyForDisplay()`
   - `maskApiKeyForDisplay()` 方法将 `UserApiKey.apiKeyEncrypted` 字段直接替换为脱敏后的明文
   - 后续其他地方尝试解密时，发现不是十六进制格式而报错

3. **错误的脱敏逻辑**：
   ```java
   // ❌ 错误：直接修改了加密字段
   private void maskApiKeyForDisplay(UserApiKey apiKey) {
       if (apiKey.getApiKeyEncrypted() != null) {
           try {
               String decryptedKey = encryptionUtil.decrypt(apiKey.getApiKeyEncrypted());
               apiKey.setApiKeyEncrypted(maskApiKey(decryptedKey)); // 破坏了原始数据
           } catch (Exception e) {
               apiKey.setApiKeyEncrypted("****");
           }
       }
   }
   ```

## 解决方案

### 1. 使用注解进行JSON序列化时脱敏

**修改 `UserApiKey` 实体类**：

```java
/**
 * 加密后的API密钥
 */
@JsonSerialize(using = GenericDataMaskingSerializer.class)
@DataMasking(strategy = MaskingStrategy.OPENAI_API_KEY)
private String apiKeyEncrypted;
```

**优势**：
- 只在JSON序列化时进行脱敏
- 不修改实体对象的原始数据
- 保持数据完整性

### 2. 移除服务层的脱敏处理

**修改前**：
```java
@Override
public List<UserApiKey> getUserApiKeysByConfigGroupId(Long userId, Long configGroupId) {
    List<UserApiKey> apiKeys = userApiKeyMapper.selectByUserIdAndConfigGroupId(userId, configGroupId);
    
    // ❌ 错误：破坏了原始数据
    return apiKeys.stream()
            .peek(this::maskApiKeyForDisplay)
            .collect(Collectors.toList());
}
```

**修改后**：
```java
@Override
public List<UserApiKey> getUserApiKeysByConfigGroupId(Long userId, Long configGroupId) {
    // ✅ 正确：直接返回原始数据，脱敏由注解处理
    return userApiKeyMapper.selectByUserIdAndConfigGroupId(userId, configGroupId);
}
```

### 3. 删除有问题的脱敏方法

完全移除了 `maskApiKeyForDisplay()` 方法，避免数据被意外修改。

## 修复内容

### 1. 实体类修改
- **文件**：`src/main/java/com/example/pure/model/entity/UserApiKey.java`
- **修改**：添加脱敏注解到 `apiKeyEncrypted` 字段

### 2. 服务层修改
- **文件**：`src/main/java/com/example/pure/service/openai/impl/AiConfigServiceImpl.java`
- **修改**：
  - 移除 `getUserApiKeys()` 中的脱敏处理
  - 移除 `getUserApiKeysByConfigGroupId()` 中的脱敏处理
  - 移除 `updateApiKey()` 中的脱敏处理
  - 删除 `maskApiKeyForDisplay()` 方法

## 脱敏机制说明

### 注解脱敏的工作原理

1. **序列化时触发**：
   ```java
   @JsonSerialize(using = GenericDataMaskingSerializer.class)
   @DataMasking(strategy = MaskingStrategy.OPENAI_API_KEY)
   ```

2. **脱敏策略**：
   ```java
   private String maskOpenaiApiKey(String apiKey) {
       if (apiKey == null || apiKey.length() <= 8) {
           return "****";
       }
       return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
   }
   ```

3. **效果**：
   - 原始数据：`"a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"`
   - 脱敏后：`"a1b2****n4o5p6"`

### 数据流对比

**修复前（有问题）**：
```
数据库 → 加密数据 → 服务层脱敏 → 明文脱敏数据 → JSON序列化 → 前端
                     ↓
                  其他服务尝试解密 → ❌ 错误：非十六进制格式
```

**修复后（正确）**：
```
数据库 → 加密数据 → 服务层（不处理） → 加密数据 → JSON序列化脱敏 → 前端
                     ↓
                  其他服务正常解密 → ✅ 成功
```

## 测试验证

### 1. 功能测试
- ✅ API密钥列表接口正常返回
- ✅ 前端显示脱敏后的密钥
- ✅ 后端服务可以正常解密使用

### 2. 数据完整性测试
- ✅ 实体对象中的加密数据未被修改
- ✅ 负载均衡服务可以正常选择和使用API密钥
- ✅ 兼容API密钥服务正常工作

### 3. 脱敏效果测试
- ✅ JSON响应中的密钥已正确脱敏
- ✅ 脱敏格式符合预期（前4位+****+后4位）

## 相关文件

### 修改的文件
- `src/main/java/com/example/pure/model/entity/UserApiKey.java`
- `src/main/java/com/example/pure/service/openai/impl/AiConfigServiceImpl.java`

### 相关的脱敏组件
- `src/main/java/com/example/pure/serializer/GenericDataMaskingSerializer.java`
- `src/main/java/com/example/pure/annotation/DataMasking.java`
- `src/main/java/com/example/pure/annotation/MaskingStrategy.java`

## 最佳实践

### 1. 数据脱敏原则
- **不修改原始数据**：脱敏应该在展示层进行，不应修改业务数据
- **使用注解脱敏**：利用JSON序列化注解进行自动脱敏
- **保持数据完整性**：确保业务逻辑可以正常使用原始数据

### 2. 避免的错误模式
- ❌ 在服务层直接修改实体对象进行脱敏
- ❌ 将脱敏后的数据传递给其他业务逻辑
- ❌ 在数据传输过程中破坏原始数据格式

### 3. 推荐的脱敏模式
- ✅ 使用JSON序列化注解进行脱敏
- ✅ 在DTO层进行数据转换和脱敏
- ✅ 保持实体对象的数据完整性

## 总结

通过将脱敏逻辑从服务层移动到JSON序列化层，我们：

1. **解决了解密错误**：保持了加密数据的完整性
2. **提高了代码质量**：遵循了单一职责原则
3. **增强了系统稳定性**：避免了数据被意外修改
4. **简化了代码逻辑**：移除了复杂的手动脱敏处理

这个修复确保了API密钥在整个系统中的数据一致性和安全性。
