# API密钥插入后字段为空问题修复

## 问题描述

在插入API密钥到数据库后，返回的DTO对象中某些字段为空：

```json
{
    "id": 3,
    "configGroupId": 3,
    "maskedApiKey": "AIza****ixyI",
    "isActive": true,
    "priority": 1,
    "usageCount": 0,
    "lastUsedAt": null,
    "createdAt": null,        // ❌ 应该有值
    "isHealthy": null,        // ❌ 应该有值
    "currentRequests": null,  // ❌ 应该有值
    "errorCount": null,       // ❌ 应该有值
    "errorRate": null         // ❌ 应该有值
}
```

## 根本原因分析

### 1. `createdAt` 字段为空
- **原因**：虽然数据库表定义了 `DEFAULT CURRENT_TIMESTAMP(3)`，但MyBatis的insert操作后，实体对象中的`createdAt`字段没有被更新
- **数据库表定义**：
  ```sql
  created_at DATETIME(3) DEFAULT CURRENT_TIMESTAMP(3)
  ```
- **MyBatis配置**：insert方法只配置了返回主键ID，没有返回其他自动生成的字段

### 2. 负载均衡相关字段为空
- **原因**：`convertToApiKeyDto`方法没有获取负载均衡状态信息
- **涉及字段**：`isHealthy`、`currentRequests`、`errorCount`、`errorRate`
- **数据来源**：这些字段需要从`api_key_load_balance`表查询

## 解决方案

### 1. 插入后重新查询（解决createdAt问题）

**修改位置**：`AiConfigServiceImpl.java` 第192-209行

**修改前**：
```java
// 插入数据库
int inserted = userApiKeyMapper.insert(userApiKey);
if (inserted > 0) {
    // 初始化负载均衡状态
    loadBalancerService.initializeLoadBalanceState(userApiKey);
    
    // 转换为DTO
    ApiKeyDto dto = convertToApiKeyDto(userApiKey);
    successKeys.add(dto);
```

**修改后**：
```java
// 插入数据库
int inserted = userApiKeyMapper.insert(userApiKey);
if (inserted > 0) {
    // 重新查询获取数据库自动生成的字段（如 createdAt）
    UserApiKey insertedApiKey = userApiKeyMapper.selectById(userApiKey.getId());
    if (insertedApiKey != null) {
        // 初始化负载均衡状态
        loadBalancerService.initializeLoadBalanceState(insertedApiKey);

        // 转换为DTO（包含负载均衡状态）
        ApiKeyDto dto = convertToApiKeyDto(insertedApiKey);
        successKeys.add(dto);
    } else {
        // 如果查询失败，使用原对象但记录警告
        log.warn("插入后查询API密钥失败 - ID: {}", userApiKey.getId());
        ApiKeyDto dto = convertToApiKeyDto(userApiKey);
        successKeys.add(dto);
    }
```

### 2. 增强convertToApiKeyDto方法（解决负载均衡字段问题）

**修改位置**：`AiConfigServiceImpl.java` 第434-467行

**修改前**：
```java
private ApiKeyDto convertToApiKeyDto(UserApiKey apiKey) {
    ApiKeyDto dto = new ApiKeyDto();
    dto.setId(apiKey.getId());
    dto.setConfigGroupId(apiKey.getConfigGroupId());
    dto.setMaskedApiKey(maskApiKey(encryptionUtil.decrypt(apiKey.getApiKeyEncrypted())));
    dto.setIsActive(apiKey.getIsActive());
    dto.setPriority(apiKey.getPriority());
    dto.setUsageCount(apiKey.getUsageCount());
    dto.setLastUsedAt(apiKey.getLastUsedAt());
    dto.setCreatedAt(apiKey.getCreatedAt());
    return dto;
}
```

**修改后**：
```java
private ApiKeyDto convertToApiKeyDto(UserApiKey apiKey) {
    ApiKeyDto dto = new ApiKeyDto();
    dto.setId(apiKey.getId());
    dto.setConfigGroupId(apiKey.getConfigGroupId());
    dto.setMaskedApiKey(maskApiKey(encryptionUtil.decrypt(apiKey.getApiKeyEncrypted())));
    dto.setIsActive(apiKey.getIsActive());
    dto.setPriority(apiKey.getPriority());
    dto.setUsageCount(apiKey.getUsageCount());
    dto.setLastUsedAt(apiKey.getLastUsedAt());
    dto.setCreatedAt(apiKey.getCreatedAt());

    // 获取负载均衡状态信息
    try {
        LoadBalancerService.LoadBalanceStats stats = loadBalancerService.getLoadBalanceStats(apiKey.getId());
        if (stats != null) {
            dto.setIsHealthy(stats.getIsHealthy());
            dto.setCurrentRequests(stats.getCurrentRequests());
            dto.setErrorCount(stats.getErrorCount());
            dto.setErrorRate(stats.getErrorRate());
        }
    } catch (Exception e) {
        log.warn("获取API密钥负载统计失败 - ID: {}", apiKey.getId(), e);
        // 设置默认值，避免返回null
        dto.setIsHealthy(true);
        dto.setCurrentRequests(0);
        dto.setErrorCount(0);
        dto.setErrorRate(0.0);
    }

    return dto;
}
```

## 修复效果

修复后，插入API密钥的响应将包含完整的字段信息：

```json
{
    "id": 3,
    "configGroupId": 3,
    "maskedApiKey": "AIza****ixyI",
    "isActive": true,
    "priority": 1,
    "usageCount": 0,
    "lastUsedAt": null,
    "createdAt": "2025-08-03T01:45:30.123Z",  // ✅ 有值
    "isHealthy": true,                         // ✅ 有值
    "currentRequests": 0,                      // ✅ 有值
    "errorCount": 0,                           // ✅ 有值
    "errorRate": 0.0                           // ✅ 有值
}
```

## 技术要点

### 1. MyBatis自动生成字段处理
- **问题**：MyBatis的`useGeneratedKeys`只能返回主键，不能返回其他自动生成字段
- **解决**：插入后立即查询，获取完整的实体信息

### 2. 负载均衡状态获取
- **数据源**：`api_key_load_balance`表
- **获取方式**：通过`LoadBalancerService.getLoadBalanceStats()`方法
- **容错处理**：如果获取失败，设置默认值避免返回null

### 3. 性能考虑
- **额外查询**：每次插入后增加一次查询操作
- **影响评估**：插入操作相对较少，性能影响可接受
- **优化空间**：可考虑使用数据库触发器或存储过程返回完整数据

## 相关文件

- `src/main/java/com/example/pure/service/openai/impl/AiConfigServiceImpl.java`
- `src/main/java/com/example/pure/controller/openai/AiConfigController.java`
- `src/main/resources/mapper/UserApiKeyMapper.xml`
- `src/main/resources/mapper/ApiKeyLoadBalanceMapper.xml`

## 测试建议

1. **功能测试**：验证插入API密钥后所有字段都有正确的值
2. **性能测试**：确认额外查询不会显著影响响应时间
3. **异常测试**：验证负载均衡状态获取失败时的容错处理
