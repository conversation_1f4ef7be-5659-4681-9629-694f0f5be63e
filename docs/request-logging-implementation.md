# 请求日志记录功能实现文档

## 概述

本文档描述了为OpenAI兼容API的`/chat/completions`和`/images/generations`端点实现详细日志记录功能的完整过程。该功能能够记录请求的完整生命周期，包括时间、状态、性能指标、错误信息等。

## 功能特性

### 1. 记录的日志信息
- ✅ **时间**: 请求开始和结束时间
- ✅ **状态**: 成功或失败
- ✅ **状态码**: HTTP状态码
- ✅ **类型**: stream或non-stream
- ✅ **花费时间**: 以毫秒为单位的请求耗时
- ✅ **重试次数**: 如果有重试机制
- ✅ **分组**: 配置分组名称
- ✅ **provider**: AI提供商(OpenAI/Anthropic/Google)
- ✅ **所使用key**: API密钥(脱敏显示)
- ✅ **错误信息**: 失败时的详细错误
- ✅ **webclient请求的实际AI大模型的基础路径**: 实际请求的BaseURL
- ✅ **请求的基础路径的后缀**: 如/chat/completions、/images/generations

### 2. 支持的端点
- ✅ `/chat/completions` - 聊天完成(流式和非流式)
- ✅ `/images/generations` - 图片生成

## 架构设计

### 1. 核心组件

#### RequestLogInfo - 日志数据模型
```java
@Data
@Builder
public class RequestLogInfo {
    private LocalDateTime startTime;        // 请求开始时间
    private LocalDateTime endTime;          // 请求结束时间
    private String status;                  // 状态(成功/失败)
    private Integer statusCode;             // HTTP状态码
    private String requestType;             // 请求类型(stream/non-stream)
    private Long durationMs;                // 花费时间(毫秒)
    private Integer retryCount;             // 重试次数
    private String groupName;               // 配置分组名称
    private String provider;                // AI提供商
    private String maskedApiKey;            // 脱敏的API密钥
    private String errorMessage;            // 错误信息
    private String actualBaseUrl;           // 实际请求的BaseURL
    private String pathSuffix;              // 请求路径后缀
    private String modelName;               // 模型名称
    private Long userId;                    // 用户ID
    private String requestId;               // 请求ID
}
```

#### RequestLogService - 日志服务
```java
@Service
public class RequestLogService {
    // 创建日志对象
    public RequestLogInfo createRequestLog();
    
    // 记录请求开始
    public void logRequestStart(RequestLogInfo logInfo, String requestType, String modelName, Long userId);
    
    // 记录请求成功
    public void logRequestSuccess(RequestLogInfo logInfo, Integer statusCode);
    
    // 记录请求失败
    public void logRequestFailure(RequestLogInfo logInfo, Integer statusCode, String errorMessage);
    
    // 设置提供商信息
    public void setProviderInfo(RequestLogInfo logInfo, String provider, String groupName, 
                               String maskedApiKey, String actualBaseUrl, String pathSuffix);
}
```

### 2. 调用链路

#### 非流式聊天完成
```
Controller.chatCompletions()
├── 创建RequestLogInfo
├── 记录请求开始
├── OpenAiRequestService.processChatCompletions(logInfo)
│   └── OpenAiCompatibleService.chatCompletions(logInfo)
│       ├── 设置提供商信息
│       ├── ModelAdapterService.chatCompletion(configGroup)
│       ├── 记录成功/失败日志
│       └── 返回响应
└── 异常处理记录失败日志
```

#### 流式聊天完成
```
Controller.chatCompletions()
├── 创建RequestLogInfo
├── 记录请求开始
├── OpenAiRequestService.processChatCompletions(logInfo)
│   └── OpenAiCompatibleService.streamChatCompletions(logInfo)
│       ├── 设置提供商信息
│       ├── processStreamChatAsync(logInfo)
│       │   ├── ModelAdapterService.streamChatCompletion(configGroup)
│       │   ├── 流式响应处理
│       │   └── 记录成功/失败日志
│       └── 返回SseEmitter
└── 异常处理记录失败日志
```

#### 图片生成
```
Controller.generateImages()
├── 创建RequestLogInfo
├── 记录请求开始
├── OpenAiRequestService.processImageGeneration(logInfo)
│   └── OpenAiCompatibleService.generateImages(logInfo)
│       ├── 设置提供商信息
│       ├── ModelAdapterService.generateImage(configGroup)
│       ├── 记录成功/失败日志
│       └── 返回响应
└── 异常处理记录失败日志
```

## 实现细节

### 1. 日志格式
```
[请求日志] 时间: 2024-01-15T10:30:15-2024-01-15T10:30:18 | 状态: 成功(200) | 类型: stream | 耗时: 3250ms | 重试: 0次 | 分组: 默认分组 | 提供商: OPENAI | 密钥: sk-1****abcd | BaseURL: https://api.openai.com/v1 | 路径: /chat/completions | 模型: gpt-4
```

### 2. API密钥脱敏
```java
public static String maskApiKey(String apiKey) {
    if (apiKey == null || apiKey.length() <= 8) {
        return "****";
    }
    return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
}
```

### 3. 性能监控
```java
public void logPerformanceMetrics(RequestLogInfo logInfo) {
    if (logInfo.getDurationMs() > 10000) { // 超过10秒
        log.warn("[性能警告] 请求耗时过长: {}ms", logInfo.getDurationMs());
    } else if (logInfo.getDurationMs() > 5000) { // 超过5秒
        log.info("[性能提醒] 请求耗时较长: {}ms", logInfo.getDurationMs());
    }
}
```

## 关键修改点

### 1. Controller层
- `OpenAiCompatibleController`: 添加RequestLogService依赖，在方法开始创建日志对象

### 2. Service层
- `OpenAiRequestService`: 接口和实现类添加RequestLogInfo参数
- `OpenAiCompatibleService`: 接口和实现类添加RequestLogInfo参数
- `OpenAiCompatibleServiceImpl`: 在关键节点记录日志信息

### 3. 新增组件
- `RequestLogInfo`: 日志数据模型
- `RequestLogService`: 日志处理服务

## 日志示例

### 成功的非流式请求
```
2024-01-15 10:30:15 INFO  - [请求开始] ID: req_a1b2c3d4 | 类型: non-stream | 模型: gpt-4 | 用户: 12345 | 时间: 2024-01-15T10:30:15
2024-01-15 10:30:18 INFO  - [请求日志] 时间: 2024-01-15T10:30:15-2024-01-15T10:30:18 | 状态: 成功(200) | 类型: non-stream | 耗时: 3250ms | 重试: 0次 | 分组: 默认分组 | 提供商: OPENAI | 密钥: sk-1****abcd | BaseURL: https://api.openai.com/v1 | 路径: /chat/completions | 模型: gpt-4
```

### 失败的流式请求
```
2024-01-15 10:35:20 INFO  - [请求开始] ID: req_e5f6g7h8 | 类型: stream | 模型: gpt-4 | 用户: 12345 | 时间: 2024-01-15T10:35:20
2024-01-15 10:35:25 ERROR - [请求日志] 时间: 2024-01-15T10:35:20-2024-01-15T10:35:25 | 状态: 失败(500) | 类型: stream | 耗时: 5000ms | 重试: 2次 | 分组: 默认分组 | 提供商: OPENAI | 密钥: sk-1****abcd | BaseURL: https://api.openai.com/v1 | 路径: /chat/completions | 模型: gpt-4 | 错误: Rate limit exceeded
```

### 图片生成请求
```
2024-01-15 10:40:10 INFO  - [请求开始] ID: req_i9j0k1l2 | 类型: non-stream | 模型: dall-e-3 | 用户: 12345 | 时间: 2024-01-15T10:40:10
2024-01-15 10:40:25 INFO  - [请求日志] 时间: 2024-01-15T10:40:10-2024-01-15T10:40:25 | 状态: 成功(200) | 类型: non-stream | 耗时: 15000ms | 重试: 0次 | 分组: 图片生成分组 | 提供商: OPENAI | 密钥: sk-1****abcd | BaseURL: https://api.openai.com/v1 | 路径: /images/generations | 模型: dall-e-3
```

## 扩展功能

### 1. 重试机制支持
```java
public void logRetry(RequestLogInfo logInfo, String reason) {
    logInfo.setRetryCount(logInfo.getRetryCount() + 1);
    log.warn("[请求重试] ID: {} | 重试次数: {} | 原因: {}", 
            logInfo.getRequestId(), logInfo.getRetryCount(), reason);
}
```

### 2. 自定义BaseURL记录
- 记录用户配置的自定义BaseURL
- 区分系统默认URL和用户自定义URL

### 3. 性能分析
- 自动识别耗时过长的请求
- 统计重试次数和成功率

## 总结

通过本次实现，系统现在具备了：
- ✅ 完整的请求生命周期日志记录
- ✅ 详细的性能和错误信息
- ✅ 结构化的日志格式
- ✅ API密钥安全脱敏
- ✅ 多端点支持(聊天和图片生成)
- ✅ 流式和非流式请求支持
- ✅ 自定义BaseURL记录
- ✅ 提供商和配置分组信息

这为系统监控、性能分析、问题排查和用户行为分析提供了强有力的支持。
