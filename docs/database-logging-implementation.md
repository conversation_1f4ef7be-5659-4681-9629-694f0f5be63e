# 数据库日志记录与分页查询功能实现文档

## 概述

本文档描述了将请求日志保存到数据库并提供分页查询功能的完整实现过程。该功能在AI大模型请求完成后自动保存日志到数据库，并通过新的控制器提供分页查询接口。

## 功能特性

### 1. 自动数据库保存
- ✅ **请求完成后自动保存** - 在`logRequestSuccess()`和`logRequestFailure()`中自动保存
- ✅ **异常安全** - 数据库保存失败不影响主流程
- ✅ **完整信息记录** - 保存所有日志字段到数据库

### 2. 分页查询功能
- ✅ **基础分页查询** - 支持页码、页大小参数
- ✅ **条件筛选** - 支持用户ID、提供商、状态、类型、模型名称、时间范围等筛选
- ✅ **失败日志查询** - 专门查询失败的请求
- ✅ **慢请求查询** - 查询耗时较长的请求
- ✅ **按提供商查询** - 按AI提供商分类查询
- ✅ **按模型查询** - 按模型名称查询

### 3. 使用PageFinalResult返回
- ✅ **标准分页格式** - 使用项目统一的PageFinalResult类
- ✅ **完整分页信息** - 包含总数、页码、页大小、总页数

## 架构设计

### 1. 数据库层

#### 数据库表结构
```sql
CREATE TABLE `request_logs` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `request_id` VARCHAR(50) NOT NULL COMMENT '请求ID（用于追踪）',
    `user_id` BIGINT NULL COMMENT '用户ID',
    `start_time` DATETIME NOT NULL COMMENT '请求开始时间',
    `end_time` DATETIME NULL COMMENT '请求结束时间',
    `status` VARCHAR(20) NOT NULL COMMENT '请求状态（成功/失败）',
    `status_code` INT NULL COMMENT 'HTTP状态码',
    `request_type` VARCHAR(20) NOT NULL COMMENT '请求类型（stream/non-stream）',
    `duration_ms` BIGINT NULL COMMENT '花费时间（毫秒）',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `group_name` VARCHAR(100) NULL COMMENT '配置分组名称',
    `provider` VARCHAR(50) NOT NULL COMMENT 'AI提供商',
    `masked_api_key` VARCHAR(50) NULL COMMENT '使用的API密钥（脱敏）',
    `error_message` TEXT NULL COMMENT '错误信息（失败时）',
    `actual_base_url` VARCHAR(500) NULL COMMENT 'WebClient请求的实际AI大模型基础路径',
    `path_suffix` VARCHAR(100) NOT NULL COMMENT '请求路径后缀',
    `model_name` VARCHAR(100) NOT NULL COMMENT '请求的模型名称',
    `extra_info` TEXT NULL COMMENT '额外信息（JSON格式）',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除（逻辑删除）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_request_id` (`request_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_provider` (`provider`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='请求日志表';
```

#### 索引优化
- **主键索引**: `id`
- **唯一索引**: `request_id`
- **查询索引**: `user_id`, `provider`, `status`, `created_at`
- **复合索引**: `user_id + created_at`, `status + created_at`

### 2. 实体层

#### RequestLog实体类
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestLog {
    private Long id;
    private String requestId;
    private Long userId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String status;
    private Integer statusCode;
    private String requestType;
    private Long durationMs;
    private Integer retryCount;
    private String groupName;
    private String provider;
    private String maskedApiKey;
    private String errorMessage;
    private String actualBaseUrl;
    private String pathSuffix;
    private String modelName;
    private String extraInfo;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean isDeleted;
}
```

### 3. 数据访问层

#### RequestLogMapper接口
```java
@Mapper
public interface RequestLogMapper {
    // 基础操作
    int insert(RequestLog requestLog);
    RequestLog selectByRequestId(String requestId);
    List<RequestLog> selectByUserId(Long userId);
    
    // 分页查询
    List<RequestLog> selectRequestLogsPage(/* 分页参数 */);
    long countRequestLogs(/* 查询条件 */);
    
    // 专项查询
    List<RequestLog> selectFailedRequestsPage(/* 失败请求分页 */);
    long countFailedRequests(/* 失败请求统计 */);
    List<RequestLog> selectSlowRequestsPage(/* 慢请求分页 */);
    long countSlowRequests(/* 慢请求统计 */);
    
    // 数据清理
    int deleteBeforeTime(LocalDateTime beforeTime);
}
```

### 4. 服务层

#### RequestLogService增强
```java
@Service
@RequiredArgsConstructor
public class RequestLogService {
    private final RequestLogMapper requestLogMapper;
    
    // 记录成功并自动保存到数据库
    public void logRequestSuccess(RequestLogInfo logInfo, Integer statusCode) {
        logInfo.setEndTime(LocalDateTime.now());
        logInfo.setSuccess(statusCode);
        logInfo.calculateDuration();
        
        log.info(logInfo.toLogString());
        
        // 自动保存到数据库
        saveToDatabase(logInfo);
    }
    
    // 记录失败并自动保存到数据库
    public void logRequestFailure(RequestLogInfo logInfo, Integer statusCode, String errorMessage) {
        logInfo.setEndTime(LocalDateTime.now());
        logInfo.setFailure(statusCode, errorMessage);
        logInfo.calculateDuration();
        
        log.error(logInfo.toLogString());
        
        // 自动保存到数据库
        saveToDatabase(logInfo);
    }
    
    // 私有方法：保存到数据库
    private void saveToDatabase(RequestLogInfo logInfo) {
        try {
            RequestLog requestLog = convertToEntity(logInfo);
            requestLogMapper.insert(requestLog);
        } catch (Exception e) {
            log.error("数据库保存失败: {}", e.getMessage(), e);
            // 不影响主流程
        }
    }
}
```

#### RequestLogQueryService查询服务
```java
@Service
@RequiredArgsConstructor
public class RequestLogQueryService {
    private final RequestLogMapper requestLogMapper;
    
    public PageFinalResult<RequestLogResponse> queryRequestLogs(RequestLogQueryRequest queryRequest) {
        // 计算分页参数
        int offset = (queryRequest.getPage() - 1) * queryRequest.getSize();
        int limit = queryRequest.getSize();
        
        // 根据查询类型执行不同的查询
        List<RequestLog> records;
        long total;
        
        if (Boolean.TRUE.equals(queryRequest.getOnlyFailed())) {
            // 查询失败请求
            records = requestLogMapper.selectFailedRequestsPage(offset, limit, ...);
            total = requestLogMapper.countFailedRequests(...);
        } else if (Boolean.TRUE.equals(queryRequest.getOnlySlow())) {
            // 查询慢请求
            records = requestLogMapper.selectSlowRequestsPage(offset, limit, ...);
            total = requestLogMapper.countSlowRequests(...);
        } else {
            // 普通查询
            records = requestLogMapper.selectRequestLogsPage(offset, limit, ...);
            total = requestLogMapper.countRequestLogs(...);
        }
        
        // 转换为响应DTO并构建分页结果
        List<RequestLogResponse> responseList = records.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
                
        PageFinalResult.PageResult pageResult = PageFinalResult.PageResult.of(
                queryRequest.getPage(), queryRequest.getSize(), total
        );
        
        return new PageFinalResult<>(responseList, pageResult);
    }
}
```

### 5. 控制器层

#### RequestLogController
```java
@RestController
@RequestMapping("/api/logs/requests")
@RequiredArgsConstructor
public class RequestLogController {
    private final RequestLogQueryService requestLogQueryService;
    
    // 基础分页查询
    @GetMapping("/page")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getRequestLogsPage(
            @Valid RequestLogQueryRequest queryRequest) {
        PageFinalResult<RequestLogResponse> result = requestLogQueryService.queryRequestLogs(queryRequest);
        return ResponseEntity.ok(result);
    }
    
    // 查询单个日志详情
    @GetMapping("/{requestId}")
    public ResponseEntity<RequestLogResponse> getRequestLogDetail(@PathVariable String requestId) {
        RequestLogResponse response = requestLogQueryService.getRequestLogByRequestId(requestId);
        return response != null ? ResponseEntity.ok(response) : ResponseEntity.notFound().build();
    }
    
    // 查询失败请求
    @GetMapping("/failed")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getFailedRequestLogs(/* 参数 */) {
        // 实现失败请求查询
    }
    
    // 查询慢请求
    @GetMapping("/slow")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getSlowRequestLogs(/* 参数 */) {
        // 实现慢请求查询
    }
    
    // 按提供商查询
    @GetMapping("/provider/{provider}")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getRequestLogsByProvider(/* 参数 */) {
        // 实现按提供商查询
    }
    
    // 按模型查询
    @GetMapping("/model/{modelName}")
    public ResponseEntity<PageFinalResult<RequestLogResponse>> getRequestLogsByModel(/* 参数 */) {
        // 实现按模型查询
    }
}
```

## API接口文档

### 1. 基础分页查询
```
GET /api/logs/requests/page?page=1&size=20&userId=123&provider=OPENAI&status=成功
```

### 2. 查询单个日志
```
GET /api/logs/requests/{requestId}
```

### 3. 查询失败请求
```
GET /api/logs/requests/failed?page=1&size=20&userId=123
```

### 4. 查询慢请求
```
GET /api/logs/requests/slow?page=1&size=20&minDuration=5000
```

### 5. 按提供商查询
```
GET /api/logs/requests/provider/OPENAI?page=1&size=20
```

### 6. 按模型查询
```
GET /api/logs/requests/model/gpt-4?page=1&size=20
```

## 自动保存机制

### 1. 触发时机
- **成功请求**: 在`RequestLogService.logRequestSuccess()`中自动保存
- **失败请求**: 在`RequestLogService.logRequestFailure()`中自动保存

### 2. 保存流程
```
AI请求完成 → 记录日志信息 → 自动保存到数据库 → 记录保存结果
```

### 3. 异常处理
- 数据库保存失败不影响主业务流程
- 记录详细的错误日志便于排查
- 使用try-catch包装确保系统稳定性

## 总结

通过本次实现，系统现在具备了：

- ✅ **自动数据库保存** - AI请求完成后自动保存日志
- ✅ **完整分页查询** - 使用PageFinalResult标准格式
- ✅ **多维度筛选** - 支持各种查询条件
- ✅ **专项查询功能** - 失败请求、慢请求等专门查询
- ✅ **RESTful API** - 标准的REST接口设计
- ✅ **性能优化** - 数据库索引和分页优化
- ✅ **异常安全** - 数据库操作不影响主流程

这为系统提供了完整的日志管理和查询分析能力，支持运维监控、问题排查和数据分析等需求。
