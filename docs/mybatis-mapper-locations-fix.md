# MyBatis Mapper XML文件位置配置问题修复

## 问题描述

当将 `OpenAIRequestLogMapper.xml` 文件从 `mapper/primary/` 目录移动到 `mapper/` 根目录时，Spring Boot启动报错，无法找到Mapper映射。

## 问题根本原因

### 1. MyBatis配置限制

**原始配置**：
```yaml
mybatis:
  mapper-locations: classpath:mapper/*.xml  # 只扫描根目录
```

**问题分析**：
- `mapper/*.xml` 只会扫描 `src/main/resources/mapper/` 根目录下的XML文件
- **不会扫描子目录**，如 `mapper/primary/`、`mapper/secondary/` 等
- 当XML文件在子目录时，MyBatis无法找到映射文件

### 2. 文件结构与配置不匹配

**项目结构**：
```
src/main/resources/mapper/
├── OpenAiRequestLogMapper.xml          # 移动到根目录
├── primary/
│   ├── UserMapper.xml                  # 在子目录
│   ├── UserApiKeyMapper.xml            # 在子目录
│   └── OpenaiRequestLogMapper.xml      # 原来的位置
└── secondary/
    └── SomeOtherMapper.xml              # 在子目录
```

**Mapper接口位置**：
```java
// 接口在 primary 包中
package com.example.pure.mapper.primary;
public interface OpenAiRequestLogMapper {
    // ...
}
```

**XML namespace**：
```xml
<!-- XML文件中的namespace指向primary包 -->
<mapper namespace="com.example.pure.mapper.primary.OpenAiRequestLogMapper">
```

### 3. 扫描规则说明

| 配置模式 | 扫描范围 | 示例 |
|---------|---------|------|
| `mapper/*.xml` | 只扫描根目录 | `mapper/UserMapper.xml` ✅<br>`mapper/primary/UserMapper.xml` ❌ |
| `mapper/**/*.xml` | 扫描所有子目录 | `mapper/primary/UserMapper.xml` ✅<br>`mapper/secondary/UserMapper.xml` ✅ |
| `mapper/*.xml, mapper/**/*.xml` | 根目录+所有子目录 | 全部扫描 ✅ |

## 解决方案

### 方案1：修改MyBatis配置（推荐）

**修改 `application.yml`**：
```yaml
mybatis:
  mapper-locations: 
    - classpath:mapper/*.xml          # 根目录下的XML文件
    - classpath:mapper/**/*.xml       # 子目录下的XML文件
  type-aliases-package: com.example.pure.model
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
```

**优势**：
- ✅ 支持灵活的文件组织结构
- ✅ 可以将XML文件放在任意子目录
- ✅ 向后兼容，不影响现有文件
- ✅ 符合大型项目的文件组织需求

### 方案2：统一文件位置

**选项A：全部放在根目录**
```
src/main/resources/mapper/
├── UserMapper.xml
├── UserApiKeyMapper.xml
├── OpenAiRequestLogMapper.xml
└── ...
```

**选项B：全部放在子目录**
```yaml
mybatis:
  mapper-locations: classpath:mapper/**/*.xml  # 只扫描子目录
```

### 方案3：修改namespace（不推荐）

如果坚持将XML放在根目录，需要：
1. 修改Mapper接口包结构
2. 修改XML的namespace
3. 更新所有相关引用

**不推荐原因**：
- 破坏现有代码结构
- 需要大量修改
- 容易引入新的错误

## 最佳实践

### 1. 文件组织建议

**按功能模块组织**：
```
src/main/resources/mapper/
├── user/
│   ├── UserMapper.xml
│   ├── UserProfileMapper.xml
│   └── UserRoleMapper.xml
├── openai/
│   ├── OpenAiRequestLogMapper.xml
│   ├── ApiKeyMapper.xml
│   └── ConfigMapper.xml
└── system/
    ├── SystemConfigMapper.xml
    └── LogMapper.xml
```

**按数据源组织**：
```
src/main/resources/mapper/
├── primary/
│   ├── UserMapper.xml
│   ├── OpenAiRequestLogMapper.xml
│   └── ...
└── secondary/
    ├── ReportMapper.xml
    └── ...
```

### 2. 配置建议

**推荐配置**：
```yaml
mybatis:
  mapper-locations: 
    - classpath:mapper/*.xml          # 根目录
    - classpath:mapper/**/*.xml       # 所有子目录
  type-aliases-package: com.example.pure.model
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
```

**优势**：
- 支持任意文件组织结构
- 便于项目扩展和维护
- 团队开发友好

### 3. 命名规范

**Mapper接口与XML文件对应**：
```
Java接口：com.example.pure.mapper.primary.UserMapper
XML文件：mapper/primary/UserMapper.xml
Namespace：com.example.pure.mapper.primary.UserMapper
```

**保持一致性**：
- 接口名 = XML文件名
- 包路径 = 目录路径
- Namespace = 完整类名

## 验证方法

### 1. 启动日志检查

查看MyBatis扫描日志：
```
DEBUG o.m.s.mapper.ClassPathMapperScanner : Identified candidate component class: 
file [C:\...\target\classes\com\example\pure\mapper\primary\OpenAiRequestLogMapper.class]
```

### 2. 配置验证

**测试命令**：
```bash
mvn clean compile -DskipTests
mvn spring-boot:run
```

**成功标志**：
- 编译无错误
- 启动无MyBatis相关异常
- 所有Mapper接口正常注入

### 3. 功能测试

**测试Mapper方法**：
```java
@Autowired
private OpenAiRequestLogMapper openAiRequestLogMapper;

@Test
public void testMapper() {
    // 测试基本的CRUD操作
    List<OpenAiRequestLog> logs = openAiRequestLogMapper.selectByUserId(1L);
    assertNotNull(logs);
}
```

## 常见错误

### 1. 路径配置错误

❌ **错误配置**：
```yaml
mybatis:
  mapper-locations: classpath:mapper/primary/*.xml  # 只扫描primary目录
```

✅ **正确配置**：
```yaml
mybatis:
  mapper-locations: 
    - classpath:mapper/*.xml
    - classpath:mapper/**/*.xml
```

### 2. Namespace不匹配

❌ **错误示例**：
```xml
<!-- XML文件在 mapper/UserMapper.xml -->
<mapper namespace="com.example.pure.mapper.primary.UserMapper">
```

✅ **正确示例**：
```xml
<!-- XML文件在 mapper/primary/UserMapper.xml -->
<mapper namespace="com.example.pure.mapper.primary.UserMapper">
```

### 3. 文件名不一致

❌ **错误示例**：
```
接口：UserMapper.java
XML：UserMapperImpl.xml  # 名称不匹配
```

✅ **正确示例**：
```
接口：UserMapper.java
XML：UserMapper.xml      # 名称匹配
```

## 总结

通过修改MyBatis的 `mapper-locations` 配置，我们解决了XML文件位置灵活性的问题：

1. **根本原因**：原配置只扫描根目录，不扫描子目录
2. **解决方案**：扩展配置同时扫描根目录和子目录
3. **最佳实践**：使用灵活的文件组织结构，便于项目维护

这个修复确保了项目可以支持任意的Mapper XML文件组织结构，提高了开发的灵活性。
