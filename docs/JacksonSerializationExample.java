ackage com.example.pure.docs;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.List;
import java.util.Arrays;

/**
 * Jackson ObjectMapper 序列化/反序列化完整示例
 * 演示如何在 Java 对象和 JSON 之间进行转换
 */
public class JacksonSerializationExample {

    // 示例数据类
    static class ContentPart {
        private int id;
        private String content;
        private String type;

        // 构造函数
        public ContentPart() {}

        public ContentPart(int id, String content, String type) {
            this.id = id;
            this.content = content;
            this.type = type;
        }

        // Getter/Setter
        public int getId() { return id; }
        public void setId(int id) { this.id = id; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        @Override
        public String toString() {
            return String.format("ContentPart{id=%d, content=
\%s\', type=\%s\'}",
                               id, content, type);
        }
    }

    public static void main(String[] args) throws Exception {
        ObjectMapper mapper = new ObjectMapper();

        // ========== 1. 单个对象序列化 ==========
        System.out.println("=== 单个对象序列化 ===");
        ContentPart part = new ContentPart(1, "Hello World", "text");

        // Java对象 → JSON字符串
        String json = mapper.writeValueAsString(part);
        System.out.println("Java对象: " + part);
        System.out.println("JSON字符串: " + json);

        // JSON字符串 → Java对象
        ContentPart deserializedPart = mapper.readValue(json, ContentPart.class);
        System.out.println("反序列化对象: " + deserializedPart);

        System.out.println();

        // ========== 2. 集合序列化（对应你的代码） ==========
        System.out.println("=== 集合序列化 ===");
        List<ContentPart> partList = Arrays.asList(
            new ContentPart(1, "第一段内容", "text"),
            new ContentPart(2, "第二段内容", "image"),
            new ContentPart(3, "第三段内容", "video")
        );

        // List<ContentPart> → JSON数组
        String listJson = mapper.writeValueAsString(partList);
        System.out.println("Java列表: " + partList);
        System.out.println("JSON数组: " + listJson);

        // ========== 3. 三种反序列化方式 ==========
        System.out.println("\n=== 三种反序列化方式 ===");

        // 方式1：使用 TypeReference（推荐）
        List<ContentPart> method1 = mapper.readValue(listJson,
            new TypeReference<List<ContentPart>>() {});
        System.out.println("方式1 - TypeReference: " + method1);

        // 方式2：使用 constructCollectionType（你的代码使用的方式）
        List<ContentPart> method2 = mapper.readValue(listJson,
            mapper.getTypeFactory().constructCollectionType(List.class, ContentPart.class));
        System.out.println("方式2 - constructCollectionType: " + method2);

        // 方式3：使用 JavaType
        var javaType = mapper.getTypeFactory()
            .constructParametricType(List.class, ContentPart.class);
        List<ContentPart> method3 = mapper.readValue(listJson, javaType);
        System.out.println("方式3 - JavaType: " + method3);

        // ========== 4. 格式化输出 ==========
        System.out.println("\n=== 格式化JSON输出 ===");
        String prettyJson = mapper.writerWithDefaultPrettyPrinter()
            .writeValueAsString(partList);
        System.out.println("格式化JSON:");
        System.out.println(prettyJson);

        // ========== 5. 处理复杂嵌套对象 ==========
        System.out.println("\n=== 嵌套对象示例 ===");

        // 创建包含列表的复杂对象
        var complexObject = new java.util.HashMap<String, Object>();
        complexObject.put("title", "内容集合");
        complexObject.put("parts", partList);
        complexObject.put("count", partList.size());

        String complexJson = mapper.writeValueAsString(complexObject);
        System.out.println("复杂对象JSON: " + complexJson);

        // 反序列化复杂对象
        var deserializedComplex = mapper.readValue(complexJson,
            new TypeReference<java.util.Map<String, Object>>() {});
        System.out.println("反序列化复杂对象: " + deserializedComplex);
    }
}
