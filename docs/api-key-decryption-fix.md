# API密钥解密失败问题修复

## 问题描述

在获取API密钥列表时出现解密错误：

```
java.lang.IllegalArgumentException: Detected a Non-hex character at 1 or 2 position
	at org.springframework.security.crypto.codec.Hex.decode(Hex.java:58)
	at org.springframework.security.crypto.encrypt.HexEncodingTextEncryptor.decrypt(HexEncodingTextEncryptor.java:44)
```

## 根本原因分析

### 1. Spring Security加密机制
- **加密器类型**：`HexEncodingTextEncryptor`
- **要求格式**：输入必须是有效的十六进制字符串
- **十六进制规则**：
  - 只能包含字符：`0-9`, `a-f`, `A-F`
  - 长度必须是偶数
  - 不能包含空格、换行符等其他字符

### 2. 可能的原因
1. **数据库编码问题**：插入时的字符编码与读取时不一致
2. **数据损坏**：数据库中存储了非十六进制字符
3. **加密方法不匹配**：使用了不同的加密算法
4. **数据截断**：数据库字段长度限制导致数据被截断

## 解决方案

### 1. 增强错误处理和诊断

**修改位置**：`SpringEncryptionUtil.java`

**新增功能**：
- 十六进制格式验证
- 详细的错误日志
- 数据格式诊断信息

```java
/**
 * 验证字符串是否为有效的十六进制格式
 */
private boolean isValidHexString(String str) {
    if (str == null || str.isEmpty()) {
        return false;
    }
    
    // 十六进制字符串长度必须是偶数
    if (str.length() % 2 != 0) {
        return false;
    }
    
    // 检查是否只包含十六进制字符
    return str.matches("^[0-9a-fA-F]+$");
}
```

### 2. 容错处理

**修改位置**：`AiConfigServiceImpl.convertToApiKeyDto()`

**改进内容**：
- 解密失败时显示友好的错误信息
- 记录详细的错误日志用于诊断
- 避免整个接口调用失败

```java
// 安全解密API密钥并脱敏
try {
    String decryptedKey = encryptionUtil.decrypt(apiKey.getApiKeyEncrypted());
    dto.setMaskedApiKey(maskApiKey(decryptedKey));
} catch (Exception e) {
    log.error("解密API密钥失败 - ID: {}, 加密数据: {}, 错误: {}", 
             apiKey.getId(), 
             apiKey.getApiKeyEncrypted() != null ? apiKey.getApiKeyEncrypted().substring(0, Math.min(20, apiKey.getApiKeyEncrypted().length())) + "..." : "null",
             e.getMessage());
    dto.setMaskedApiKey("****解密失败****");
}
```

## 数据诊断和修复

### 1. 检查问题数据

执行以下SQL查询来识别问题数据：

```sql
-- 查看所有API密钥的加密数据格式
SELECT 
    id,
    user_id,
    config_group_id,
    LENGTH(api_key_encrypted) as encrypted_length,
    LEFT(api_key_encrypted, 50) as encrypted_preview,
    CASE 
        WHEN api_key_encrypted REGEXP '^[0-9a-fA-F]+$' THEN 'VALID_HEX'
        ELSE 'INVALID_HEX'
    END as format_status,
    created_at
FROM user_api_keys 
ORDER BY id;

-- 查找非十六进制格式的记录
SELECT 
    id,
    user_id,
    api_key_encrypted,
    LENGTH(api_key_encrypted) as length,
    created_at
FROM user_api_keys 
WHERE NOT (api_key_encrypted REGEXP '^[0-9a-fA-F]+$')
   OR LENGTH(api_key_encrypted) % 2 != 0;
```

### 2. 数据修复选项

#### 选项A：重新加密（推荐）
如果您还有原始的API密钥，可以重新加密：

```java
// 在应用程序中重新加密
String originalApiKey = "your-original-api-key";
String reencrypted = encryptionUtil.encrypt(originalApiKey);

// 更新数据库
UPDATE user_api_keys 
SET api_key_encrypted = ? 
WHERE id = ?;
```

#### 选项B：删除损坏的记录
如果无法恢复原始密钥：

```sql
-- 删除无法解密的记录（谨慎操作）
DELETE FROM user_api_keys 
WHERE NOT (api_key_encrypted REGEXP '^[0-9a-fA-F]+$')
   OR LENGTH(api_key_encrypted) % 2 != 0;
```

#### 选项C：标记为无效
保留记录但标记为无效：

```sql
-- 将损坏的记录标记为无效
UPDATE user_api_keys 
SET is_active = FALSE,
    api_key_encrypted = 'CORRUPTED_DATA'
WHERE NOT (api_key_encrypted REGEXP '^[0-9a-fA-F]+$')
   OR LENGTH(api_key_encrypted) % 2 != 0;
```

## 预防措施

### 1. 数据库字段配置
确保数据库字段有足够的长度：

```sql
-- 检查字段定义
DESCRIBE user_api_keys;

-- 如果需要，增加字段长度
ALTER TABLE user_api_keys 
MODIFY COLUMN api_key_encrypted TEXT;
```

### 2. 加密测试
在插入前测试加密/解密循环：

```java
// 在保存前验证加密/解密循环
String originalKey = "test-api-key";
String encrypted = encryptionUtil.encrypt(originalKey);
String decrypted = encryptionUtil.decrypt(encrypted);

if (!originalKey.equals(decrypted)) {
    throw new RuntimeException("加密/解密验证失败");
}
```

### 3. 数据完整性检查
定期检查数据完整性：

```sql
-- 创建数据完整性检查视图
CREATE VIEW api_key_integrity_check AS
SELECT 
    id,
    user_id,
    CASE 
        WHEN api_key_encrypted REGEXP '^[0-9a-fA-F]+$' 
         AND LENGTH(api_key_encrypted) % 2 = 0 
        THEN 'OK'
        ELSE 'CORRUPTED'
    END as integrity_status,
    created_at
FROM user_api_keys;
```

## 修复效果

修复后的系统将：

1. **优雅处理解密失败**：不会导致整个API调用失败
2. **提供详细诊断信息**：帮助快速定位问题数据
3. **显示友好错误信息**：用户界面显示"****解密失败****"而不是系统错误
4. **记录详细日志**：便于运维人员排查问题

## 测试建议

1. **功能测试**：
   - 测试正常API密钥的加密/解密
   - 测试损坏数据的容错处理
   - 验证用户界面的错误显示

2. **数据验证**：
   - 运行SQL查询检查数据完整性
   - 验证所有现有密钥都能正常解密

3. **日志监控**：
   - 监控解密失败的日志
   - 及时发现和处理数据问题

## 相关文件

- `src/main/java/com/example/pure/util/SpringEncryptionUtil.java`
- `src/main/java/com/example/pure/service/openai/impl/AiConfigServiceImpl.java`
- `src/main/resources/schema.sql`
